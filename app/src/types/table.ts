import type { ReactNode } from "react";

import type { TextFieldProps } from "@mui/material/TextField";
import type { SxProps, Theme } from "@mui/material/styles";

export type Order = "asc" | "desc";

export type Column<Row> = {
  id: keyof Row | string;
  label: string;
  width?: number | string;
  align?: "left" | "right" | "center" | "inherit" | "justify";
  sortable?: boolean;
  getValue?: (row: Row) => ReactNode;
  renderCell?: (row: Row) => ReactNode;
  getSortValue?: (row: Row) => string | number;
  sticky?: "left" | "right";
  headerSx?: SxProps<Theme>;
};

export type BaseDataTableProps<Row extends { id: string }> = {
  columns: Column<Row>[];
  rows: Row[];
  loading?: boolean;
  orderBy?: string;
  order?: Order;
  onSortChange?: (orderBy: string, order: Order) => void;
  selected?: string[];
  onSelectionChange?: (ids: string[]) => void;
  getRowHighlight?: (row: Row) => "issue" | undefined;
  onActionClick?: (row: Row, action: string) => void;
  height?: number | string;
  enableRowReorder?: boolean;
  onRowOrderChange?: (rows: Row[]) => void;
};

export enum TABLE_WIDTH {
  SELECT_COL_WIDTH = 48,
  ACTIONS_COL_WIDTH = 72,
}

export type PaintLineRow = {
  id: string;
  feedCount: number;
  feedStyle: string;
  order: string | number;
  feedBatch: string;
  uniqueWidth: number;
  slitWidths: string;
  slitPattern: string;
  wastagePct: number;
  hasIssue?: boolean;
};

export type PaintLineTableProps = {
  rows: PaintLineRow[];
  orderBy?: string;
  order?: Order;
  onSortChange?: (orderBy: string, order: Order) => void;
  selected?: string[];
  onSelectionChange?: (ids: string[]) => void;
  enableRowReorder?: boolean;
  onRowOrderChange?: (rows: PaintLineRow[]) => void;
};

export type NestingRow = {
  id: string;
  feedCount: number;
  feedStyle: string;
  order: string | number;
  feedBatch: string;
  uniqueWidth: number;
  slitWidths: string;
  slitPattern: string;
  wastagePct: number;
  hasIssue?: boolean;
};

export type NestingTableProps = {
  rows: NestingRow[];
  orderBy?: string;
  order?: Order;
  onSortChange?: (orderBy: string, order: Order) => void;
  selected?: string[];
  onSelectionChange?: (ids: string[]) => void;
  enableRowReorder?: boolean;
  onRowOrderChange?: (rows: NestingRow[]) => void;
};

export type PriorityRow = {
  id: string;
  order: string | number;
  material: string;
  materialDescription: string;
  ou: string;
  mrpController: string;
  comOn: string;
  itemQuantity: string;
  ou2: string;
  basicFinishDay: string;
  priority?: string;
  vol?: string;
};

export type PriorityTableProps = {
  rows: PriorityRow[];
  orderBy?: string;
  order?: Order;
  onSortChange?: (orderBy: string, order: Order) => void;
  selected?: string[];
  onSelectionChange?: (ids: string[]) => void;
  enableRowReorder?: boolean;
  onRowOrderChange?: (rows: PriorityRow[]) => void;
};

export type ColumnsButtonProps = {
  onClick?: () => void;
  size?: "small" | "medium" | "large";
};

export type SearchBarProps = {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  ariaLabel?: string;
  textFieldProps?: Omit<
    TextFieldProps,
    "value" | "onChange" | "placeholder" | "size" | "sx"
  >;
};
