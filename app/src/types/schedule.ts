import type { PriorityRow } from "@/types/table";

export interface CreateScheduleSplitButtonProps {
  onCreate?: () => void;
  onSelectTemplate?: (templateId: string) => void;
  options?: { id: string; label: string }[];
}

export const STEP_HEADER_GAP = 8;

export const CONTINUE_BTN_SX = {
  borderRadius: 1.5,
  px: 3,
  textTransform: "none",
  fontWeight: 600,
  bgcolor: "grey.900",
  color: "common.white",
  border: "1px solid",
  borderColor: "grey.900",
  "&:hover": { bgcolor: "grey.800", borderColor: "grey.800" },
  "&.Mui-disabled": {
    bgcolor: "grey.900",
    borderColor: "grey.900",
    color: "common.white",
    opacity: 0.5,
  },
} as const;

export type Step2Props = {
  rows: PriorityRow[];
  searchValue: string;
  onSearchChange: (v: string) => void;
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
};

export const SEARCH_INPUT_SX = {
  width: { xs: "100%", sm: 260 },
  "& .MuiInputBase-root": {
    borderRadius: 1.5,
    backgroundColor: "background.paper",
  },
} as const;

export type WizardStep = 0 | 1 | 2 | 3 | 4;

export type WizardMode = "individual" | "zip";

export type NumberFieldProps = {
  label: string;
  value: number;
  onChange: (n: number) => void;
  min?: number;
  max?: number;
  step?: number;
  ariaLabel?: string;
};

export const REVERT_BTN_SX = {
  textTransform: "none",
  borderRadius: "6px",
  minWidth: "auto",
  minHeight: 40,
  px: 2,
  py: 1,
  fontSize: 13,
  color: "text.primary",
  border: "1px solid",
  borderColor: "divider",
  bgcolor: "transparent",
  "&:hover": { borderColor: "divider", bgcolor: "transparent" },
} as const;

export const APPLY_BTN_SX = {
  ...CONTINUE_BTN_SX,
  borderRadius: "6px",
  minHeight: 40,
  px: 2,
  py: 1,
  fontSize: 13,
} as const;

export const ACTIONS_ROW_SX = {
  display: "flex",
  justifyContent: "flex-end",
  gap: 2,
  mt: 3,
} as const;

export type Step1Props = {
  mode: "individual" | "zip";
  onModeChange: (mode: "individual" | "zip") => void;

  zipName: string;
  stockName: string;
  planName: string;
  ordersName: string;

  onZipNameChange: (name: string) => void;
  onStockNameChange: (name: string) => void;
  onPlanNameChange: (name: string) => void;
  onOrdersNameChange: (name: string) => void;

  hasPlanError: boolean;
};

export const GRID_WRAPPER_SX = {
  display: "grid",
  gridTemplateColumns: { xs: "1fr", md: "340px 1fr" },
  gap: 3,
  alignItems: "start",
} as const;

export const SECTION_GRID_SX = {
  display: "grid",
  gridTemplateColumns: "24px 1fr",
  columnGap: 1,
} as const;

export const TITLE_SX = { fontSize: 14, fontWeight: 700, mb: 0.5 } as const;

export type Step3Props = { onStartOptimisation?: () => void };

export type FacilityOption = { id: string; label: string };

export interface ParameterConfigurationSectionProps {
  site: string;
  onSiteChange: (site: string) => void;
  facilityOptions: FacilityOption[];
}
