import { ROUTES } from "@/types/routes";

export interface SwitchOption {
  label: string;
  value: string;
  color?: string;
}

export interface PrimaryNavTabsProps {
  basePath: string;
}

export interface SelectionSwitcherProps {
  options: readonly SwitchOption[] | SwitchOption[];
  value: string;
  onChange: (value: string) => void;
  size?: "small" | "medium";
  minWidth?: number;
  "aria-label"?: string;
}

export const ARRAY_ROUTES = [
  { label: "Coil Nesting", value: ROUTES.COIL_NESTING, color: "grey.300" },
  {
    label: "Paint Line",
    value: ROUTES.PAINT_LINE,
    color: "gradients.paintLine",
  },
] as const;

export type SelectIconProps = { className?: string };

export type ThemeColorToken = string | undefined;

export const DOT_SIZE = 12;

export const TABS = [
  { label: "Dashboard", subPath: "" },
  { label: "Optimise", subPath: "optimise" },
] as const;
