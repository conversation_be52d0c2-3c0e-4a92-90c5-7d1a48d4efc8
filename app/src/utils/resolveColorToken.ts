import type { ThemeColorToken } from "@/types/header";

import type { Theme } from "@mui/material/styles";

export function resolveColorToken(
  token: ThemeColorToken,
  theme: Theme
): string | undefined {
  if (!token) return undefined;

  if (token.startsWith("gradients.")) {
    const key = token.split(".")[1] as keyof Theme["gradients"];
    return theme.gradients?.[key];
  }

  if (token.includes(".")) {
    const [group, shade] = token.split(".");
    const paletteAny = theme.palette as unknown as Record<string, unknown>;
    const groupObj = paletteAny[group] as Record<string, unknown> | undefined;
    if (groupObj && Object.prototype.hasOwnProperty.call(groupObj, shade)) {
      return String(groupObj[shade]);
    }
  }
  return token;
}
