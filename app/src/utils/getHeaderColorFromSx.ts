import type { SxProps, Theme } from "@mui/material/styles";

export const getHeaderColorFromSx = (
  sx?: SxProps<Theme>
): string | undefined => {
  if (!sx) return undefined;
  if (Array.isArray(sx)) {
    for (const part of sx) {
      if (part && typeof part === "object" && !Array.isArray(part)) {
        const color = (part as { color?: unknown }).color;
        if (typeof color === "string") return color;
      }
    }
    return undefined;
  }
  if (typeof sx === "function") return undefined;
  if (typeof sx === "object") {
    const color = (sx as { color?: unknown }).color;
    return typeof color === "string" ? color : undefined;
  }
  return undefined;
};
