import { type RefObject, useLayoutEffect, useState } from "react";

export function useOutlinedInputHeight(
  inputRef: RefObject<HTMLInputElement>,
  fallback = 48
) {
  const [height, setHeight] = useState<number>(fallback);

  useLayoutEffect(() => {
    let frameId: number | null = null;
    let detach: (() => void) | null = null;

    const getRoot = () =>
      (inputRef.current?.closest(
        ".MuiOutlinedInput-root"
      ) as HTMLElement | null) ?? null;

    const update = () => {
      const root = getRoot();
      if (root) setHeight(root.offsetHeight || fallback);
    };

    const setup = () => {
      const root = getRoot();
      if (root) {
        update();
        const hasRO =
          typeof window !== "undefined" && "ResizeObserver" in window;
        if (hasRO) {
          const ro = new ResizeObserver(() => update());
          ro.observe(root);
          detach = () => ro.disconnect();
        } else {
          window.addEventListener("resize", update);
          detach = () => window.removeEventListener("resize", update);
        }
      } else {
        // If the input isn't mounted yet (e.g. conditional UI), try again next frame
        frameId = window.requestAnimationFrame(setup);
      }
    };

    setup();

    return () => {
      if (frameId != null) window.cancelAnimationFrame(frameId);
      if (detach) detach();
    };
  }, [inputRef, fallback]);

  return height;
}
