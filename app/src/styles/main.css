@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @font-face {
    font-family: "Maison Neue";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url("/fonts/MaisonNeue-Regular.woff2") format("woff2");
  }
  @font-face {
    font-family: "Maison Neue";
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url("/fonts/MaisonNeue-Bold.woff2") format("woff2");
  }

  body {
    font-family:
      "Maison Neue",
      system-ui,
      -apple-system,
      "Segoe UI",
      Roboto,
      Arial,
      "Noto Sans",
      sans-serif,
      "Apple Color Emoji",
      "Segoe UI Emoji",
      "Segoe UI Symbol";
    color: #1a2027;
    margin: 0;
    background-color: #fff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  .logo {
    height: 6rem;
    padding: 1.5rem;
    will-change: filter;
    transition: filter 300ms;
  }
  .logo:hover {
    filter: drop-shadow(0 0 2rem #646cffaa);
  }
  .logo.react:hover {
    filter: drop-shadow(0 0 2rem #61dafbaa);
  }

  @keyframes logo-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @media (prefers-reduced-motion: no-preference) {
    a:nth-of-type(2) .logo {
      animation: logo-spin infinite 20s linear;
    }
  }
  @media (prefers-reduced-motion: reduce) {
    a:nth-of-type(2) .logo {
      animation: none;
    }
  }

  .card {
    padding: 2rem;
  }
  .read-the-docs {
    color: #888;
  }
}

@layer components {
  .auth-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
  }

  .auth-button {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition:
      background-color 0.2s ease,
      box-shadow 0.2s ease,
      transform 0.1s ease,
      border-color 0.2s ease,
      color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .auth-button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  .auth-button:focus-visible {
    outline: 2px solid #106ebe;
    outline-offset: 2px;
  }

  .auth-button.login {
    background: linear-gradient(135deg, #0078d4, #106ebe);
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
  }
  .auth-button.login:hover:not(:disabled) {
    background: linear-gradient(135deg, #106ebe, #005a9e);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 120, 212, 0.4);
  }

  .auth-button.minimal {
    background: #f7f7f7;
    color: #111;
    border: 1px solid #c7c7c7;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
  }
  .auth-button.minimal:hover:not(:disabled) {
    background: #efefef;
  }

  .auth-button.logout {
    background: #f3f2f1;
    color: #323130;
    border: 1px solid #d2d0ce;
  }
  .auth-button.logout:hover:not(:disabled) {
    background: #edebe9;
    border-color: #c8c6c4;
  }

  .auth-button.loading {
    background: #f3f2f1;
    color: #605e5c;
  }

  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .spinner {
      animation: none;
    }
    .auth-button {
      transition: none;
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e4e8;
  }
  .user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .user-name {
    font-weight: 600;
    color: #24292e;
    font-size: 14px;
  }
  .user-email {
    font-size: 12px;
    color: #586069;
    margin-top: 2px;
  }

  .auth-button.login::before {
    content: "\01F3E2";
    margin-right: 4px;
  }
  .auth-button.login.minimal::before,
  .auth-button.minimal::before {
    content: none;
    margin-right: 0;
  }

  @media (max-width: 768px) {
    .user-info {
      flex-direction: column;
      text-align: center;
    }
    .auth-button {
      width: 100%;
      justify-content: center;
    }
  }
}
