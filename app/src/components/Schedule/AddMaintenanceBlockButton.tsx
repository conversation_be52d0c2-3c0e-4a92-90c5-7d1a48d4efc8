import Button, { type ButtonProps } from "@mui/material/Button";

export type AddMaintenanceBlockButtonProps = Omit<ButtonProps, "children"> & {
  label?: string;
};

export const AddMaintenanceBlockButton = ({
  label = "Add maintenance block",
  ...props
}: AddMaintenanceBlockButtonProps) => {
  return (
    <Button
      variant="outlined"
      size="small"
      sx={{ textTransform: "none", borderRadius: 1 }}
      {...props}
    >
      {label}
    </Button>
  );
};

export default AddMaintenanceBlockButton;
