import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import LinearProgress from "@mui/material/LinearProgress";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

export interface OptimizationInProgressPanelProps {
  onUseThisOptimisation?: () => void;
  onStop?: () => void;
  hideBestSoFar?: boolean;
}

export const OptimizationProgressPanel = ({
  onUseThisOptimisation,
  onStop,
  hideBestSoFar = false,
}: OptimizationInProgressPanelProps) => (
  <Paper
    variant="outlined"
    sx={(theme) => ({
      borderRadius: 3,
      borderColor: "divider",
      bgcolor: theme.palette.grey[100],
      px: { xs: 2, md: 3 },
      py: { xs: 2.5, md: 4 },
      textAlign: "center",
      mb: 5,
      maxWidth: { xs: "100%", md: 880 },
      mx: "auto",
    })}
  >
    <Typography sx={{ fontSize: { xs: 22, md: 24 }, fontWeight: 800, mb: 0.5 }}>
      Optimization in progress...
    </Typography>

    <Typography
      variant="caption"
      color="text.secondary"
      sx={{ display: "block", mb: 2 }}
    >
      13m 42 sec remaining...
    </Typography>

    <Button
      variant="contained"
      color="error"
      sx={{
        borderRadius: 1.5,
        px: 2.5,
        textTransform: "none",
        fontWeight: 600,
        mb: 3,
      }}
      onClick={onStop}
    >
      Stop optimisation
    </Button>

    {!hideBestSoFar && (
      <Box
        sx={{
          maxWidth: { xs: "100%", md: 560 },
          mx: "auto",
          textAlign: "left",
        }}
      >
        <Paper
          variant="outlined"
          sx={{
            borderRadius: 2,
            px: { xs: 2, md: 3 },
            py: 2.5,
            bgcolor: "background.paper",
          }}
        >
          <Typography sx={{ fontSize: 16, fontWeight: 700, mb: 0.5 }}>
            Most optimized schedule so far...
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            &quot;Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
            do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
            enim ad minim veniam...&quot;
          </Typography>

          <Typography sx={{ fontWeight: 800, mb: 0.75 }}>
            12,584{" "}
            <Box component="span" sx={{ fontWeight: 500 }}>
              Lorem ipsum
            </Box>
          </Typography>
          <LinearProgress
            variant="determinate"
            value={82}
            sx={(theme) => ({
              height: 10,
              borderRadius: 1,
              bgcolor: theme.palette.grey[300],
              ["& .MuiLinearProgress-bar"]: {
                bgcolor: theme.palette.grey[800],
                borderRadius: 1,
              },
            })}
            aria-label="Best score so far"
          />

          <Typography sx={{ fontWeight: 700, mt: 2, mb: 0.75 }}>
            10,103{" "}
            <Box component="span" sx={{ fontWeight: 500 }}>
              Lorem ipsum
            </Box>
          </Typography>
          <LinearProgress
            variant="determinate"
            value={42}
            sx={(theme) => ({
              height: 10,
              borderRadius: 1,
              bgcolor: theme.palette.grey[200],
              ["& .MuiLinearProgress-bar"]: {
                bgcolor: theme.palette.grey[400],
                borderRadius: 1,
              },
            })}
            aria-label="Second best score"
          />

          <Box sx={{ display: "flex", justifyContent: "center", mt: 2.5 }}>
            <Button
              variant="contained"
              sx={{
                borderRadius: 1.5,
                textTransform: "none",
                fontWeight: 600,
              }}
              onClick={onUseThisOptimisation}
            >
              Use this optimisation
            </Button>
          </Box>

          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ display: "block", mt: 1.25, textAlign: "center" }}
          >
            This will stop the optimization process and use the most optimized
            schedule generated so far
          </Typography>
        </Paper>
      </Box>
    )}
  </Paper>
);
