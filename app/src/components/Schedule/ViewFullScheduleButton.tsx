import Button, { type ButtonProps } from "@mui/material/Button";

export type ViewFullScheduleButtonProps = Omit<
  ButtonProps,
  "variant" | "size" | "children" | "aria-label"
> & {
  label?: string;
};

export const ViewFullScheduleButton = ({
  label = "View full schedule",
  sx,
  ...props
}: ViewFullScheduleButtonProps) => (
  <Button
    variant="outlined"
    size="small"
    sx={{ textTransform: "none", borderRadius: 1, ...sx }}
    aria-label={label}
    {...props}
  >
    {label}
  </Button>
);
