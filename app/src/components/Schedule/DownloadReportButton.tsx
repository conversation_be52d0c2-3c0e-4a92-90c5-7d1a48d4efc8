import Button, { type ButtonProps } from "@mui/material/Button";

export type DownloadReportButtonProps = Omit<
  ButtonProps,
  "children" | "aria-label"
> & {
  label?: string;
};

export const DownloadReportButton = ({
  label = "Download report",
  sx,
  ...props
}: DownloadReportButtonProps) => (
  <Button
    variant="outlined"
    size="small"
    sx={{ textTransform: "none", borderRadius: 1, ...sx }}
    aria-label={label}
    {...props}
  >
    {label}
  </Button>
);
