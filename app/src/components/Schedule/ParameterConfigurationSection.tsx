import { SecondaryNavTabs } from "@/components/CoilNesting/SecondaryNavTabs";
import { FacilitySelect } from "@/components/Facility/FacilitySelect";
import { LabeledNumberField } from "@/components/ui/LabeledNumberField";
import { ACTIONS_ROW_SX, APPLY_BTN_SX, REVERT_BTN_SX } from "@/types/schedule";
import type { ParameterConfigurationSectionProps } from "@/types/schedule";

import { useState } from "react";

import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import ReplayRoundedIcon from "@mui/icons-material/ReplayRounded";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Paper from "@mui/material/Paper";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";

export const ParameterConfigurationSection = ({
  site,
  onSiteChange,
  facilityOptions,
}: ParameterConfigurationSectionProps) => {
  const [tab, setTab] = useState(0);
  const [daysToRun, setDaysToRun] = useState(100);

  const [fields, setFields] = useState({
    wideBandWidth: 100,
    minCuttableWidth: 360,
    narrowBandWidth: 30,
    narrowBandGauge: 0.3,
    narrowBandRubbers: 0,
    arbourInUse: 3,
    minProdToFill: 99,
    maxProdToFill: 125,
    feedCoilsToRun: 20,
    maxUniqueWidthsPerPattern: 5,
    maxUniqueHeadsets: 6,
    forecastOrderHorizonWeeks: 4,
    maxFeedCoilCuts: 4,
  });

  const setField = (key: keyof typeof fields, value: number) =>
    setFields((s) => ({ ...s, [key]: value }));

  return (
    <Box>
      <Box sx={{ mb: 1 }}>
        <Typography
          sx={{
            fontSize: 12,
            fontWeight: 700,
            color: "text.primary",
            letterSpacing: "0.08em",
            mb: 1,
          }}
        >
          OPTIONAL
        </Typography>
        <FacilitySelect
          value={site}
          onChange={onSiteChange}
          options={facilityOptions}
          ariaLabel="Select site"
          minWidth={220}
        />
      </Box>

      <Typography
        component="h2"
        sx={{ fontSize: { xs: 24, md: 28 }, fontWeight: 800, mb: 0.75 }}
      >
        Parameter configuration
      </Typography>
      <Typography variant="body2" color="text.primary" sx={{ mb: 3 }}>
        the adjustment of many different parameters relating to the
        optimisation.
      </Typography>

      <Box sx={{ mb: 5 }}>
        <SecondaryNavTabs
          labels={["General", "Advanced config"]}
          value={tab}
          onChange={setTab}
          ariaLabel="Parameter configuration tabs"
        />
      </Box>

      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 340px" },
          gap: 3,
          alignItems: "start",
        }}
      >
        <Paper
          variant="outlined"
          sx={{ p: 2.5, borderRadius: 2, borderColor: "divider" }}
        >
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography
              sx={{ fontSize: { xs: 18, md: 20 }, fontWeight: 700, mb: 1.5 }}
            >
              Overall parameters
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Allow for the adjustment of many different parameters relating to
              the optimisation.
            </Typography>

            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: { xs: "1fr", md: "repeat(3, 1fr)" },
                columnGap: { xs: 0, md: 0.5 },
                rowGap: { xs: 1.9, md: 1.9 },
                alignItems: "start",
              }}
            >
              <Box sx={{ display: "flex", flexDirection: "column", gap: 4 }}>
                <LabeledNumberField
                  label="Wide Band Width (mm)"
                  value={fields.wideBandWidth}
                  onChange={(v) => setField("wideBandWidth", v)}
                />
                <LabeledNumberField
                  label="Min Cuttable Width (mm)"
                  value={fields.minCuttableWidth}
                  onChange={(v) => setField("minCuttableWidth", v)}
                />
                <LabeledNumberField
                  label="Narrow Band Width (mm)"
                  value={fields.narrowBandWidth}
                  onChange={(v) => setField("narrowBandWidth", v)}
                />
                <LabeledNumberField
                  label="Narrow Band Gauge (mm)"
                  value={fields.narrowBandGauge}
                  onChange={(v) => setField("narrowBandGauge", v)}
                  step={0.1}
                />
                <LabeledNumberField
                  label="Narrow Band Rubbers (#)"
                  value={fields.narrowBandRubbers}
                  onChange={(v) => setField("narrowBandRubbers", v)}
                  min={0}
                />
              </Box>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 4 }}>
                <LabeledNumberField
                  label="Arbours in Use (#)"
                  value={fields.arbourInUse}
                  onChange={(v) => setField("arbourInUse", v)}
                  min={0}
                />
                <LabeledNumberField
                  label="Minimum Prod to Fill (%)"
                  value={fields.minProdToFill}
                  onChange={(v) => setField("minProdToFill", v)}
                  min={0}
                  max={100}
                />
                <LabeledNumberField
                  label="Maximum Prod to Fill (%)"
                  value={fields.maxProdToFill}
                  onChange={(v) => setField("maxProdToFill", v)}
                  min={0}
                  max={100}
                />
                <LabeledNumberField
                  label="Feed Coils to Run"
                  value={fields.feedCoilsToRun}
                  onChange={(v) => setField("feedCoilsToRun", v)}
                  min={0}
                />
                <LabeledNumberField
                  label="Max Unique Widths per Pattern (#)"
                  value={fields.maxUniqueWidthsPerPattern}
                  onChange={(v) => setField("maxUniqueWidthsPerPattern", v)}
                  min={0}
                />
              </Box>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 4 }}>
                <LabeledNumberField
                  label="Max Unique Headsets (#)"
                  value={fields.maxUniqueHeadsets}
                  onChange={(v) => setField("maxUniqueHeadsets", v)}
                  min={0}
                />
                <LabeledNumberField
                  label="Forecast Order Horizon in Weeks (#)"
                  value={fields.forecastOrderHorizonWeeks}
                  onChange={(v) => setField("forecastOrderHorizonWeeks", v)}
                  min={0}
                />
                <LabeledNumberField
                  label="Max Feed Coil Cuts (#)"
                  value={fields.maxFeedCoilCuts}
                  onChange={(v) => setField("maxFeedCoilCuts", v)}
                  min={0}
                />
              </Box>
            </Box>
          </Box>

          <Box sx={ACTIONS_ROW_SX}>
            <Button
              variant="outlined"
              size="medium"
              startIcon={<ReplayRoundedIcon />}
              sx={REVERT_BTN_SX}
            >
              Revert changes
            </Button>
            <Button variant="contained" size="medium" sx={APPLY_BTN_SX}>
              Save & Apply
            </Button>
          </Box>
        </Paper>

        <Paper
          variant="outlined"
          sx={{ p: 2.5, borderRadius: 2, borderColor: "divider" }}
        >
          <Box
            sx={{ display: "flex", alignItems: "center", gap: 0.75, mb: 0.5 }}
          >
            <Typography sx={{ fontSize: { xs: 18, md: 20 }, fontWeight: 700 }}>
              Days to run
            </Typography>
            <Tooltip
              title="Increasing this will also increase the time it takes for the tool to run. Please update with care."
              arrow
            >
              <InfoOutlinedIcon fontSize="small" color="action" />
            </Tooltip>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Increasing this will also increase the time it takes for the tool to
            run. Please update with care.
          </Typography>

          <LabeledNumberField
            label="Days the tool should plan for (#)"
            value={daysToRun}
            onChange={setDaysToRun}
            min={1}
          />

          <Box sx={ACTIONS_ROW_SX}>
            <Button
              variant="outlined"
              size="medium"
              startIcon={<ReplayRoundedIcon />}
              sx={REVERT_BTN_SX}
            >
              Revert changes
            </Button>
            <Button variant="contained" size="medium" sx={APPLY_BTN_SX}>
              Save & Apply
            </Button>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};
