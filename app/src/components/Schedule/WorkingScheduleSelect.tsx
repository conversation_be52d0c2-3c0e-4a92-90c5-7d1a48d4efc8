import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";

export type WorkingScheduleSelectProps = {
  value: string;
  onChange: (value: string) => void;
  options?: string[];
};

export const WorkingScheduleSelect = ({
  value,
  onChange,
  options = ["2323", "2324", "2325"],
}: WorkingScheduleSelectProps) => (
  <Select
    size="small"
    displayEmpty
    value={value}
    onChange={(e) => onChange(String(e.target.value))}
    sx={{
      minWidth: 180,
      borderRadius: 1.5,
      backgroundColor: "background.paper",
    }}
    renderValue={(v) => (v ? String(v) : "Select")}
    aria-label="Working schedule number"
  >
    <MenuItem value="">Select</MenuItem>
    {options.map((opt) => (
      <MenuItem key={opt} value={opt}>
        {opt}
      </MenuItem>
    ))}
  </Select>
);

export default WorkingScheduleSelect;
