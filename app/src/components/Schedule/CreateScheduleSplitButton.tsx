import { ChevronDownIcon, DocumentPlusIcon } from "@/components/ui/icons";
import type { CreateScheduleSplitButtonProps } from "@/types/schedule";

import { useId, useState } from "react";

import Button from "@mui/material/Button";
import ButtonGroup from "@mui/material/ButtonGroup";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";

export const CreateScheduleSplitButton = ({
  onCreate,
  onSelectTemplate,
  options = [
    { id: "blank", label: "Blank schedule" },
    { id: "from-template", label: "From template…" },
  ],
}: CreateScheduleSplitButtonProps) => {
  const menuId = useId();
  const menuButtonId = useId();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleToggle = (e: { currentTarget: HTMLElement | null }) => {
    setAnchorEl((prev) => (prev ? null : e.currentTarget));
  };

  const handleClose = () => setAnchorEl(null);

  const handleCreate = () => {
    onCreate?.();
  };

  const handleSelect = (id: string) => () => {
    onSelectTemplate?.(id);
    handleClose();
  };

  return (
    <>
      <ButtonGroup
        variant="contained"
        aria-label="Create new schedule actions"
        sx={(theme) => ({
          boxShadow: "none",
          borderRadius: "20px",
          overflow: "hidden",
          "& .MuiButtonGroup-grouped:not(:last-of-type)": {
            borderRight: `1px solid ${theme.palette.common.white}26`,
          },
        })}
      >
        <Button
          type="button"
          disableElevation
          onClick={handleCreate}
          startIcon={<DocumentPlusIcon sx={{ fontSize: 16 }} />}
          sx={(theme) => ({
            bgcolor: theme.palette.grey[700],
            color: theme.palette.common.white,
            px: 2.5,
            py: 0.875,
            fontSize: 13,
            fontWeight: 500,
            letterSpacing: "-0.01em",
            textTransform: "none",
            borderRadius: 0,
            "&:hover": {
              bgcolor: theme.palette.grey[800],
            },
          })}
        >
          Create new schedule
        </Button>
        <Button
          type="button"
          disableElevation
          id={menuButtonId}
          aria-label="More create options"
          aria-haspopup="menu"
          aria-controls={open ? menuId : undefined}
          aria-expanded={open ? "true" : undefined}
          onClick={handleToggle}
          sx={(theme) => ({
            bgcolor: theme.palette.grey[700],
            color: theme.palette.common.white,
            minWidth: 36,
            px: 1,
            borderRadius: 0,
            "&:hover": {
              bgcolor: theme.palette.grey[800],
            },
          })}
        >
          <ChevronDownIcon sx={{ fontSize: 16 }} />
        </Button>
      </ButtonGroup>

      <Menu
        id={menuId}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        slotProps={{ list: { "aria-labelledby": menuButtonId } }}
        sx={{
          mt: 0.5,
          "& .MuiPaper-root": {
            borderRadius: 1,
          },
        }}
      >
        {options.map((o) => (
          <MenuItem
            key={o.id}
            onClick={handleSelect(o.id)}
            sx={{ fontSize: 13, py: 0.75 }}
          >
            {o.label}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default CreateScheduleSplitButton;
