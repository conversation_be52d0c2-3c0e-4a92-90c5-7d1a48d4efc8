import uploadSvg from "@/assets/upload.svg";
import { useOutlinedInputHeight } from "@/utils/useOutlinedInputHeight";

import { type ReactNode, useRef } from "react";

import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import TextField, { type TextFieldProps } from "@mui/material/TextField";

type AccessibleFileUploadProps = {
  id?: string;
  value: string;
  placeholder?: string;
  accept?: string;
  onChangeValue: (next: string) => void;
  "aria-label"?: string;
  error?: boolean;
  endAdornment?: ReactNode;
  textFieldProps?: Omit<
    TextFieldProps,
    "value" | "onChange" | "placeholder" | "error" | "slotProps"
  >;
  fileNameFromFile?: (file: File) => string;
};

const FIELD_SX = {
  "& .MuiInputBase-root": {
    borderRadius: 1.5,
    backgroundColor: "background.paper",
  },
} as const;

const UPLOAD_ICON_SX = {
  width: "100%",
  height: "100%",
  display: "block",
  backgroundColor: "transparent",
} as const;

export const AccessibleFileUpload = ({
  id,
  value,
  placeholder,
  accept,
  onChangeValue,
  "aria-label": ariaLabel,
  error,
  endAdornment,
  textFieldProps,
  fileNameFromFile,
}: AccessibleFileUploadProps) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const height = useOutlinedInputHeight(inputRef);
  const size = Math.max(40, height || 0);

  const getName = fileNameFromFile || ((f: File) => f.name);

  return (
    <Box sx={{ display: "flex", gap: 1, alignItems: "stretch" }}>
      <TextField
        id={id}
        fullWidth
        size="small"
        value={value}
        onChange={(e) => onChangeValue(e.target.value)}
        placeholder={placeholder}
        error={error}
        inputRef={inputRef}
        slotProps={{
          input: {
            endAdornment: endAdornment ? (
              <InputAdornment position="end">{endAdornment}</InputAdornment>
            ) : undefined,
          },
        }}
        sx={FIELD_SX}
        {...textFieldProps}
      />

      <IconButton
        component="label"
        aria-label={ariaLabel || "Upload"}
        disableRipple
        disableFocusRipple
        disableTouchRipple
        sx={{
          width: size,
          height: size,
          minWidth: size,
          minHeight: size,
          borderRadius: 1.5,
          bgcolor: "grey.900",
          color: "common.white",
          border: "1px solid",
          borderColor: "grey.900",
          "&:hover": { bgcolor: "grey.800", borderColor: "grey.800" },
          display: "inline-flex",
          alignItems: "center",
          justifyContent: "center",
          flex: "0 0 auto",
          p: 0,
        }}
      >
        <Box
          component="img"
          src={uploadSvg}
          alt=""
          aria-hidden
          sx={UPLOAD_ICON_SX}
        />
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          hidden
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) onChangeValue(getName(file));
            e.currentTarget.value = "";
          }}
        />
      </IconButton>
    </Box>
  );
};
