import { AccessibleFileUpload } from "@/components/Upload/AccessibleFileUpload";
import { CheckOutlineIcon, ErrorInfoIcon } from "@/components/ui/icons";
import { SECTION_GRID_SX, type Step1Props, TITLE_SX } from "@/types/schedule";

import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

export type Step1UploadPanelProps = Pick<
  Step1Props,
  | "mode"
  | "zipName"
  | "stockName"
  | "planName"
  | "ordersName"
  | "onZipNameChange"
  | "onStockNameChange"
  | "onPlanNameChange"
  | "onOrdersNameChange"
  | "hasPlanError"
>;

export const Step1UploadPanel = ({
  mode,
  zipName,
  stockName,
  planName,
  ordersName,
  onZipNameChange,
  onStockNameChange,
  onPlanNameChange,
  onOrdersNameChange,
  hasPlanError,
}: Step1UploadPanelProps) => (
  <Paper
    variant="outlined"
    sx={{
      p: 3,
      borderRadius: 3,
      bgcolor: "background.default",
      borderColor: "divider",
    }}
  >
    {mode === "zip" ? (
      <Box sx={{ ...SECTION_GRID_SX, rowGap: 1.25 }}>
        <Typography
          sx={{ fontSize: 14, fontWeight: 700, textAlign: "right", pr: 0.5 }}
        >
          1.
        </Typography>
        <Box>
          <Typography component="span" sx={TITLE_SX}>
            Fully zipped file{" "}
            <Box component="span" sx={{ color: "error.main" }}>
              *
            </Box>
          </Typography>
        </Box>

        <Box />
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          Please ensure all 3 files are in this zip folder (Stock, plan & order)
        </Typography>

        <Box />
        <AccessibleFileUpload
          value={zipName}
          onChangeValue={onZipNameChange}
          placeholder="2323_3294856.zip"
          accept=".zip,application/zip"
          aria-label="Upload zip"
        />

        <Box />
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mt: 0.25 }}>
          <CheckOutlineIcon sx={{ color: "success.main" }} />
          <Typography sx={{ fontSize: 13 }}>2323_3294856_STOCK.csx</Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontStyle: "italic" }}
          >
            is ready for upload
          </Typography>
        </Box>
      </Box>
    ) : (
      <Box sx={{ display: "grid", gap: 2 }}>
        <Box sx={{ ...SECTION_GRID_SX, rowGap: 1.25 }}>
          <Typography
            sx={{
              fontSize: 14,
              fontWeight: 700,
              textAlign: "right",
              pr: 0.5,
            }}
          >
            1.
          </Typography>
          <Typography sx={TITLE_SX}>
            Stock file{" "}
            <Box component="span" sx={{ color: "error.main" }}>
              *
            </Box>
          </Typography>

          <Box />
          <Typography variant="body2" color="text.secondary">
            This file will be renamed automatically once uploaded
          </Typography>

          <Box />
          <AccessibleFileUpload
            value={stockName}
            onChangeValue={onStockNameChange}
            placeholder="2323_3294856.csx"
            accept=".csx"
            aria-label="Upload stock"
          />

          <Box />
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, mt: 0.25 }}>
            <CheckOutlineIcon sx={{ color: "success.main" }} />
            <Typography sx={{ fontSize: 13 }}>
              2323_3294856_STOCK.csx
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontStyle: "italic" }}
            >
              is ready for upload
            </Typography>
          </Box>
        </Box>

        <Box sx={{ ...SECTION_GRID_SX, rowGap: 1.25 }}>
          <Typography
            sx={{
              fontSize: 14,
              fontWeight: 700,
              textAlign: "right",
              pr: 0.5,
            }}
          >
            2.
          </Typography>
          <Typography sx={TITLE_SX}>
            Plan file{" "}
            <Box component="span" sx={{ color: "error.main" }}>
              *
            </Box>
          </Typography>

          <Box />
          <Typography variant="body2" color="text.secondary">
            This file will be renamed automatically once uploaded
          </Typography>

          <Box />
          <AccessibleFileUpload
            value={planName}
            onChangeValue={onPlanNameChange}
            placeholder="1234_235457_Plan_order.csx"
            accept=".csx"
            aria-label="Upload plan"
            error={hasPlanError}
            endAdornment={
              hasPlanError ? (
                <Box sx={{ color: "error.main" }}>
                  <ErrorInfoIcon />
                </Box>
              ) : undefined
            }
            fileNameFromFile={(f) => f.name.replace(/\.[^.]+$/, "")}
          />

          {hasPlanError ? (
            <>
              <Box />
              <Typography variant="body2" sx={{ color: "error.main", mt: 0.5 }}>
                Please exclude other key words in the file name. E.g. the word
                “order” should not be in the name for the plan file
              </Typography>
            </>
          ) : null}
        </Box>

        <Box sx={{ ...SECTION_GRID_SX, rowGap: 1.25 }}>
          <Typography
            sx={{
              fontSize: 14,
              fontWeight: 700,
              textAlign: "right",
              pr: 0.5,
            }}
          >
            3.
          </Typography>
          <Typography sx={TITLE_SX}>
            Orders file{" "}
            <Box component="span" sx={{ color: "error.main" }}>
              *
            </Box>
          </Typography>

          <Box />
          <Typography variant="body2" color="text.secondary">
            This file will be renamed automatically once uploaded
          </Typography>

          <Box />
          <AccessibleFileUpload
            value={ordersName}
            onChangeValue={onOrdersNameChange}
            placeholder="2323_3294856.csx"
            accept=".csx"
            aria-label="Upload orders"
          />
        </Box>
      </Box>
    )}
  </Paper>
);
