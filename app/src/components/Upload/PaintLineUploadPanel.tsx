import { AccessibleFileUpload } from "@/components/Upload/AccessibleFileUpload";
import { UploadButton } from "@/components/Upload/UploadButton";
import { CheckOutlineIcon } from "@/components/ui/icons";
import { SECTION_GRID_SX, TITLE_SX } from "@/types/schedule";

import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

export type ScheduleCsvUploadSectionProps = {
  scheduleName: string;
  onChangeScheduleName: (name: string) => void;
  onUploadClick?: () => void;
  placeholder?: string;
  accept?: string;
  ariaLabel?: string;
};

export const ScheduleCsvUploadSection = ({
  scheduleName,
  onChangeScheduleName,
  onUploadClick,
  placeholder = "2323_3294856.csx",
  accept = ".csx,.csv,text/csv",
  ariaLabel = "Upload schedule",
}: ScheduleCsvUploadSectionProps) => (
  <>
    <Paper
      variant="outlined"
      sx={{
        p: 3,
        borderRadius: 3,
        bgcolor: "background.default",
        borderColor: "divider",
      }}
    >
      <Box sx={{ ...SECTION_GRID_SX, rowGap: 1.25 }}>
        <Typography
          sx={{ fontSize: 14, fontWeight: 700, textAlign: "right", pr: 0.5 }}
        >
          1.
        </Typography>
        <Box>
          <Typography component="span" sx={TITLE_SX}>
            Upload schedule CSV file{" "}
            <Box component="span" sx={{ color: "error.main" }}>
              *
            </Box>
          </Typography>
        </Box>

        <Box />
        <AccessibleFileUpload
          value={scheduleName}
          onChangeValue={onChangeScheduleName}
          placeholder={placeholder}
          accept={accept}
          aria-label={ariaLabel}
        />

        <Box />
        {scheduleName ? (
          <Box
            sx={{ display: "flex", alignItems: "center", gap: 1, mt: 0.25 }}
            aria-live="polite"
          >
            <CheckOutlineIcon sx={{ color: "success.main" }} />
            <Typography sx={{ fontSize: 13 }}>{scheduleName}</Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontStyle: "italic" }}
            >
              is ready for upload
            </Typography>
          </Box>
        ) : null}
      </Box>
    </Paper>

    <Box
      sx={{
        display: "flex",
        justifyContent: { xs: "flex-start", md: "flex-end" },
        mt: 2.5,
      }}
    >
      <UploadButton onClick={onUploadClick} disabled={!scheduleName} />
    </Box>
  </>
);
