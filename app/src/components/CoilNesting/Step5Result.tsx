import warningSvg from "@/assets/warning.svg";
import { NestingTable } from "@/components/CoilNesting/NestingTable";
import { SecondaryNavTabs } from "@/components/CoilNesting/SecondaryNavTabs";
import { ColumnsButton } from "@/components/ui/ColumnsButton";
import { SearchBar } from "@/components/ui/SearchBar";
import { ROUTES } from "@/types/routes";
import type { NestingRow } from "@/types/table";

import { useCallback, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import Paper from "@mui/material/Paper";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useTheme } from "@mui/material/styles";

export const Step5Result = () => {
  const navigate = useNavigate();
  const theme = useTheme();

  const [tab, setTab] = useState(1);
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState<string[]>([]);
  const [orderBy, setOrderBy] = useState<string | undefined>(undefined);
  const [order, setOrder] = useState<"asc" | "desc">("asc");

  const [rows, setRows] = useState<NestingRow[]>(() =>
    Array.from({ length: 12 }).map((_, i) => ({
      id: String(i + 1),
      feedCount: (i % 3) + 1,
      feedStyle: ["A", "B", "C"][i % 3],
      order: `12${345 + i}`,
      feedBatch: `B-${10 + i}`,
      uniqueWidth: 1180 + ((i * 5) % 40),
      slitWidths: "100,150,200",
      slitPattern: ["AAB", "ABC", "CCC"][i % 3],
      wastagePct: 1.2 + (i % 5) * 0.8,
      hasIssue: i % 4 === 1,
    }))
  );

  const filteredRows = useMemo(() => {
    if (!search) return rows;
    const q = search.toLowerCase();
    return rows.filter(
      (r) =>
        String(r.order).toLowerCase().includes(q) ||
        r.feedBatch.toLowerCase().includes(q)
    );
  }, [rows, search]);

  const handleSortChange = useCallback((by: string, ord: "asc" | "desc") => {
    setOrderBy(by);
    setOrder(ord);
  }, []);

  const handleRowOrderChange = useCallback((nextSubset: NestingRow[]) => {
    setRows((prev) => {
      const subsetIds = new Set(nextSubset.map((r) => r.id));
      const queue = nextSubset.slice();
      return prev.map((row) =>
        subsetIds.has(row.id) ? (queue.shift() ?? row) : row
      );
    });
  }, []);

  return (
    <Box component="main">
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 0,
        }}
      >
        <Typography
          component="h1"
          sx={{ fontSize: { xs: 22, md: 24 }, fontWeight: 800 }}
        >
          Optimised schedule_12476586
        </Typography>
        <Button
          variant="outlined"
          size="small"
          sx={{ textTransform: "none", borderRadius: 1 }}
          onClick={() => navigate(ROUTES.COIL_NESTING)}
        >
          Finish
        </Button>
      </Box>

      <Box
        sx={{
          mt: { xs: 3, md: 4 },
          bgcolor: theme.palette.grey[100],
          borderRadius: 2,
          p: 2,
        }}
      >
        <SecondaryNavTabs
          labels={["Summary", "Warnings", "Configuration"]}
          value={tab}
          onChange={setTab}
          ariaLabel="Result sections"
          containerBg={theme.palette.grey[100]}
        />

        {tab === 1 && (
          <Stack
            direction={{ xs: "column", md: "row" }}
            spacing={2}
            sx={{ mt: 2 }}
          >
            {[
              "Not enough paint available",
              "Another warning",
              "Another warning",
            ].map((text, idx) => (
              <Paper
                key={text + idx}
                variant="outlined"
                sx={{
                  flex: 1,
                  borderRadius: 2,
                  p: 2,
                  bgcolor: "background.paper",
                }}
              >
                <Stack direction="row" spacing={1.5} alignItems="flex-start">
                  <Box
                    component="img"
                    src={warningSvg}
                    alt=""
                    sx={{ height: 18, width: "auto" }}
                  />
                  <Box>
                    <Typography
                      sx={{ fontSize: 13, fontWeight: 600, mb: 0.25 }}
                    >
                      {text}
                    </Typography>
                    {idx === 0 && (
                      <Typography variant="caption" color="text.secondary">
                        Sched No 3, 10, 11
                      </Typography>
                    )}
                  </Box>
                </Stack>
              </Paper>
            ))}
          </Stack>
        )}
      </Box>

      <Typography
        variant="h6"
        sx={{
          mt: 4,
          mb: { xs: 2, md: 2.5 },
          fontSize: 14,
          fontWeight: 700,
          color: "grey.900",
        }}
      >
        Orders
      </Typography>

      <Stack
        direction={{ xs: "column", md: "row" }}
        spacing={1.5}
        alignItems={{ xs: "stretch", md: "center" }}
        sx={{ mb: { xs: 2, md: 3 } }}
      >
        <Stack direction="row" spacing={1}>
          <Button
            variant="contained"
            color="inherit"
            sx={{ borderRadius: 1.5, textTransform: "none" }}
          >
            View selected patterns
          </Button>
          <Button
            variant="outlined"
            sx={{ textTransform: "none", borderRadius: 1 }}
          >
            Re-run optimisation
          </Button>
        </Stack>
        <Box sx={{ flex: 1 }} />
        <SearchBar
          value={search}
          onChange={setSearch}
          ariaLabel="Search orders"
        />
        <ColumnsButton />
      </Stack>

      <NestingTable
        rows={filteredRows}
        selected={selected}
        onSelectionChange={setSelected}
        orderBy={orderBy}
        order={order}
        onSortChange={handleSortChange}
        enableRowReorder
        onRowOrderChange={handleRowOrderChange}
      />

      <Divider sx={{ my: 4 }} />
    </Box>
  );
};
