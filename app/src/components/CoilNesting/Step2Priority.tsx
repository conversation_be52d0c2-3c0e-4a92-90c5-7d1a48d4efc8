import { PriorityTable } from "@/components/CoilNesting/PriorityTable";
import { ColumnsButton } from "@/components/ui/ColumnsButton";
import { SearchBar } from "@/components/ui/SearchBar";
import { type Step2Props } from "@/types/schedule";

import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

export const Step2Priority = ({
  rows,
  searchValue,
  onSearchChange,
  selectedIds,
  onSelectionChange,
}: Step2Props) => {
  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 1.5,
          mb: 1.25,
          flexWrap: "wrap",
        }}
      >
        <Typography sx={{ fontSize: 16, fontWeight: 700 }}>Orders</Typography>
        <Box sx={{ flex: 1 }} />
        <SearchBar value={searchValue} onChange={onSearchChange} />
        <ColumnsButton />
      </Box>

      <Paper
        variant="outlined"
        sx={{
          p: 0,
          borderRadius: 2,
          bgcolor: "grey.100",
          borderColor: "divider",
          mb: 3,
        }}
      >
        <PriorityTable
          rows={rows}
          selected={selectedIds}
          onSelectionChange={onSelectionChange}
        />
      </Paper>
    </>
  );
};
