import { BaseDataTable } from "@/components/Table/BaseDataTable";
import type { Column, PriorityRow, PriorityTableProps } from "@/types/table";

import TextField from "@mui/material/TextField";

export const PriorityTable = ({
  rows,
  orderBy,
  order,
  onSortChange,
  selected,
  onSelectionChange,
  enableRowReorder = true,
  onRowOrderChange = () => {},
}: PriorityTableProps) => {
  const headerColor = "text.secondary" as const;
  const columns: Column<PriorityRow>[] = [
    {
      id: "order",
      label: "Order",
      width: 120,
      headerSx: { color: headerColor },
    },
    {
      id: "material",
      label: "Material",
      width: 140,
      headerSx: { color: headerColor },
    },
    {
      id: "materialDescription",
      label: "Material Description",
      width: 200,
      headerSx: { color: headerColor },
    },
    { id: "ou", label: "OU", width: 80, headerSx: { color: headerColor } },
    {
      id: "mrpController",
      label: "MRP controller",
      width: 140,
      headerSx: { color: headerColor },
    },
    {
      id: "comOn",
      label: "ComOn",
      width: 120,
      headerSx: { color: headerColor },
    },
    {
      id: "itemQuantity",
      label: "Item quantity",
      width: 140,
      headerSx: { color: headerColor },
    },
    { id: "ou2", label: "OU", width: 80, headerSx: { color: headerColor } },
    {
      id: "basicFinishDay",
      label: "Basic finish day",
      width: 160,
      headerSx: { color: headerColor },
    },
    {
      id: "priority",
      label: "Priority",
      width: 140,
      headerSx: { color: headerColor },
      renderCell: (r) => (
        <TextField
          size="small"
          placeholder=""
          value={r.priority ?? ""}
          onChange={() => {}}
        />
      ),
    },
    {
      id: "vol",
      label: "Vol",
      width: 140,
      headerSx: { color: headerColor },
      renderCell: (r) => (
        <TextField
          size="small"
          placeholder=""
          value={r.vol ?? ""}
          onChange={() => {}}
        />
      ),
    },
  ];

  return (
    <BaseDataTable
      columns={columns}
      rows={rows}
      orderBy={orderBy}
      order={order}
      onSortChange={onSortChange}
      selected={selected}
      onSelectionChange={onSelectionChange}
      height={520}
      enableRowReorder={enableRowReorder}
      onRowOrderChange={onRowOrderChange}
    />
  );
};
