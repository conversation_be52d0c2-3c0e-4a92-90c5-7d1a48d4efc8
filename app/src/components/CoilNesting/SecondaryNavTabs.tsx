import { type SyntheticEvent, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import Box from "@mui/material/Box";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";

export type SecondaryNavTabsProps = {
  basePath?: string;
  labels?: readonly string[] | string[];
  value?: number;
  onChange?: (index: number) => void;
  ariaLabel?: string;
  containerBg?: string;
};

const TABS = [
  { label: "Dashboard", subPath: "" },
  { label: "Past schedules", subPath: "past-schedules" },
  { label: "Configuration", subPath: "configuration" },
  { label: "OD", subPath: "od" },
] as const;

export const SecondaryNavTabs = ({
  basePath,
  labels,
  value,
  onChange,
  ariaLabel,
  containerBg,
}: SecondaryNavTabsProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  const isRouteMode = Boolean(basePath);

  const baseNoTrailing = useMemo(() => {
    if (!basePath) return "";
    return basePath.replace(/\/+$/, "");
  }, [basePath]);

  const routeIndex = useMemo(() => {
    if (!isRouteMode) return 0;
    let rel = location.pathname;
    const absBase = baseNoTrailing.startsWith("/")
      ? baseNoTrailing
      : `/${baseNoTrailing}`;
    if (rel.startsWith(absBase)) {
      rel = rel.slice(absBase.length);
    } else if (rel.startsWith(baseNoTrailing)) {
      rel = rel.slice(baseNoTrailing.length);
    }
    rel = rel.replace(/^\/+/, "");

    const first = rel.split("/")[0] || "";
    const idx = TABS.findIndex((t) => t.subPath === first);
    return idx === -1 ? 0 : idx;
  }, [isRouteMode, location.pathname, baseNoTrailing]);

  const handleRouteChange = (_: SyntheticEvent, index: number) => {
    const tab = TABS[index];
    const path = tab.subPath
      ? `${baseNoTrailing}/${tab.subPath}`
      : baseNoTrailing;
    if (path !== location.pathname) {
      navigate(path);
    }
  };

  const localLabels = useMemo(() => (labels ? [...labels] : []), [labels]);
  const localIndex = useMemo(() => {
    const v = value ?? 0;
    if (v < 0) return 0;
    if (v >= localLabels.length) return Math.max(0, localLabels.length - 1);
    return v;
  }, [value, localLabels.length]);

  const handleLocalChange = (_: SyntheticEvent, index: number) => {
    onChange?.(index);
  };

  const tabsToRender = isRouteMode ? TABS.map((t) => t.label) : localLabels;
  const currentIndex = isRouteMode ? routeIndex : localIndex;
  const changeHandler = isRouteMode ? handleRouteChange : handleLocalChange;
  const aria =
    ariaLabel ||
    (isRouteMode
      ? "Coil Nesting secondary navigation"
      : "Secondary navigation");

  return (
    <Box sx={{ width: "max-content" }}>
      <Tabs
        value={currentIndex}
        onChange={changeHandler}
        aria-label={aria}
        variant="scrollable"
        scrollButtons="auto"
        textColor="primary"
        indicatorColor="primary"
        slotProps={{ indicator: { sx: { display: "none" } } }}
        sx={(theme) => ({
          mt: 2,
          p: 0.5,
          minHeight: 36,
          bgcolor: containerBg ?? theme.palette.background.paper,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: "8px",
          "& .MuiTabs-flexContainer": { height: 36, gap: 2 },
          "& .MuiTab-root": {
            minHeight: 32,
            minWidth: "auto",
            px: 7.5,
            mx: 0,
            borderRadius: "6px",
            textTransform: "none",
            fontSize: 13,
            fontWeight: 500,
            letterSpacing: "-0.01em",
            color: theme.palette.grey[700],
            transition: "background-color 0.2s ease",
            "&:hover": {
              bgcolor: theme.palette.grey[50] || theme.palette.grey[100],
            },
            "&.Mui-selected": {
              color: theme.palette.grey[900],
              fontWeight: 700,
              bgcolor: theme.palette.grey[200],
            },
          },
        })}
      >
        {tabsToRender.map((label) => (
          <Tab key={label} label={label} disableRipple />
        ))}
      </Tabs>
    </Box>
  );
};

export default SecondaryNavTabs;
