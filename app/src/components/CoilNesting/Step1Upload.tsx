import { Step1UploadPanel } from "@/components/Upload/CoilNestingUploadPanel";
import { RadioCardOption } from "@/components/ui/RadioCardOption";
import { type Step1Props } from "@/types/schedule";
import { GRID_WRAPPER_SX } from "@/types/schedule";

import Box from "@mui/material/Box";
import RadioGroup from "@mui/material/RadioGroup";
import Typography from "@mui/material/Typography";

export const Step1Upload = ({
  mode,
  onModeChange,
  zipName,
  stockName,
  planName,
  ordersName,
  onZipNameChange,
  onStockNameChange,
  onPlanNameChange,
  onOrdersNameChange,
  hasPlanError,
}: Step1Props) => {
  return (
    <Box sx={GRID_WRAPPER_SX}>
      <Box>
        <Typography
          component="h1"
          sx={{
            fontSize: { xs: 24, md: 32 },
            fontWeight: 800,
            mb: 1.5,
            lineHeight: 1.2,
          }}
        >
          Upload files
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Choose how you wish to upload the three files needed for optimisation.
          (Stock, plan & orders)
        </Typography>

        <RadioGroup
          value={mode}
          onChange={(_, v) => onModeChange(v as "individual" | "zip")}
          aria-label="Upload mode selection"
        >
          <RadioCardOption
            value="individual"
            selected={mode === "individual"}
            title="Individual files"
            description="You can upload the 3 files individually (Stock, plan and orders)"
            onSelect={() => onModeChange("individual")}
          />
          <Typography
            variant="body2"
            align="center"
            color="text.secondary"
            sx={{ my: 1 }}
          >
            or
          </Typography>
          <RadioCardOption
            value="zip"
            selected={mode === "zip"}
            title="Zipped folder"
            description="Upload the fully zipped folder that contains the 3 files (stock, plan and orders)"
            onSelect={() => onModeChange("zip")}
          />
        </RadioGroup>
      </Box>

      <Step1UploadPanel
        mode={mode}
        zipName={zipName}
        stockName={stockName}
        planName={planName}
        ordersName={ordersName}
        onZipNameChange={onZipNameChange}
        onStockNameChange={onStockNameChange}
        onPlanNameChange={onPlanNameChange}
        onOrdersNameChange={onOrdersNameChange}
        hasPlanError={hasPlanError}
      />
    </Box>
  );
};
