import hotelSvg from "@/assets/hotel.svg";
import { ParameterConfigurationSection } from "@/components/Schedule/ParameterConfigurationSection";
import { CONTINUE_BTN_SX } from "@/types/schedule";
import { type Step3Props } from "@/types/schedule";

import { useState } from "react";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Chip from "@mui/material/Chip";
import Divider from "@mui/material/Divider";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

export const Step3Parameters = ({ onStartOptimisation }: Step3Props) => {
  const facilityOptions = [{ id: "site-1", label: "Site 1 name" }];
  const [site, setSite] = useState<string>(facilityOptions[0].id);
  const currentSiteLabel =
    facilityOptions.find((o) => o.id === site)?.label ?? site;

  return (
    <Box>
      <Paper
        variant="outlined"
        sx={(theme) => ({
          borderRadius: 3,
          borderColor: "divider",
          bgcolor: theme.palette.grey[100],
          px: { xs: 2, md: 3 },
          py: { xs: 2.5, md: 4 },
          textAlign: "center",
          mb: 5,
          maxWidth: { xs: "100%", md: 560 },
          mx: "auto",
        })}
      >
        <Typography
          sx={{ fontSize: { xs: 24, md: 28 }, fontWeight: 800, mb: 0.75 }}
        >
          Begin optimization process for
        </Typography>

        <Box sx={{ display: "flex", justifyContent: "center", mb: 1.5 }}>
          <Chip
            icon={
              <Box
                component="img"
                src={hotelSvg}
                alt=""
                aria-hidden
                sx={{ width: 18, height: 18, display: "block" }}
              />
            }
            label={`[${currentSiteLabel}] schedule`}
            size="small"
            sx={(theme) => ({
              bgcolor: "transparent",
              border: 0,
              fontWeight: 600,
              color: theme.palette.text.primary,
            })}
          />
        </Box>

        <Typography variant="body2" color="text.primary" sx={{ mb: 2 }}>
          You may now start the optimisation process, or configure some
          parameters below.
        </Typography>

        <Button
          variant="contained"
          sx={CONTINUE_BTN_SX}
          onClick={onStartOptimisation}
        >
          Start optimisation
        </Button>
      </Paper>

      <Divider sx={{ my: 4 }} />

      <ParameterConfigurationSection
        site={site}
        onSiteChange={setSite}
        facilityOptions={facilityOptions}
      />
    </Box>
  );
};
