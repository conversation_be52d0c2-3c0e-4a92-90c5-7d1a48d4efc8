import { OptimizationProgressPanel } from "@/components/Schedule/OptimizationProgressPanel";
import { ParameterConfigurationSection } from "@/components/Schedule/ParameterConfigurationSection";

import { useState } from "react";

import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";

export const Step4Run = ({
  onUseThisOptimisation,
}: {
  onUseThisOptimisation?: () => void;
}) => {
  const facilityOptions = [{ id: "site-1", label: "Site 1 name" }];
  const [site, setSite] = useState<string>(facilityOptions[0].id);

  return (
    <Box>
      <OptimizationProgressPanel
        onUseThisOptimisation={onUseThisOptimisation}
      />

      <Divider sx={{ my: 4 }} />

      <ParameterConfigurationSection
        site={site}
        onSiteChange={setSite}
        facilityOptions={facilityOptions}
      />
    </Box>
  );
};
