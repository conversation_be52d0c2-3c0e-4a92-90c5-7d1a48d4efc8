import { ActionsMenu, type MenuPosition } from "@/components/ui/ActionsMenu";
import { DragIndicatorIcon, MoreHorizIcon } from "@/components/ui/icons";
import type { BaseDataTableProps, Order } from "@/types/table";
import { getHeaderColorFromSx } from "@/utils/getHeaderColorFromSx";
import { normalizeForSort } from "@/utils/normalizeForSort";
import { stableSort } from "@/utils/sort";
import { toCellContent } from "@/utils/toCellContent";

import {
  type CSSProperties,
  type ChangeEvent,
  type RefCallback,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import {
  DndContext,
  type DragEndEvent,
  DragOverlay,
  type DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
  restrictToWindowEdges,
} from "@dnd-kit/modifiers";
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import Box from "@mui/material/Box";
import Checkbox from "@mui/material/Checkbox";
import IconButton from "@mui/material/IconButton";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableSortLabel from "@mui/material/TableSortLabel";

import { ActionsCell } from "./ActionsCell";
import { InactiveSortIcon } from "./InactiveSortIcon";
import { SelectCell } from "./SelectCell";
import { StickyCellLeft } from "./StickyCellLeft";
import { StickyCellRight } from "./StickyCellRight";

export function BaseDataTable<Row extends { id: string }>(
  props: BaseDataTableProps<Row>
) {
  const {
    columns,
    rows,
    orderBy,
    order = "asc",
    onSortChange,
    selected,
    onSelectionChange,
    getRowHighlight,
    onActionClick,
    height = 520,
    enableRowReorder,
    onRowOrderChange,
  } = props;

  const allIds = useMemo(() => rows.map((r) => r.id), [rows]);
  const selectedSet = useMemo(() => new Set(selected ?? []), [selected]);
  const allSelected =
    selectedSet.size > 0 && allIds.every((id) => selectedSet.has(id));
  const someSelected = selectedSet.size > 0 && !allSelected;

  const handleToggleAll = (e: ChangeEvent<HTMLInputElement>) => {
    if (!onSelectionChange) return;
    onSelectionChange(e.target.checked ? allIds : []);
  };
  const handleToggleOne =
    (id: string) => (e: ChangeEvent<HTMLInputElement>) => {
      if (!onSelectionChange) return;
      const next = new Set(selectedSet);
      if (e.target.checked) next.add(id);
      else next.delete(id);
      onSelectionChange(Array.from(next));
    };

  const getComparator = useCallback(
    (orderByKey?: string, orderDir: Order = "asc") =>
      (a: Row, b: Row) => {
        if (!orderByKey) return 0;
        const col = columns.find((c) => String(c.id) === orderByKey);
        const va = col?.getSortValue
          ? col.getSortValue(a)
          : normalizeForSort((a as Record<string, unknown>)[orderByKey]);
        const vb = col?.getSortValue
          ? col.getSortValue(b)
          : normalizeForSort((b as Record<string, unknown>)[orderByKey]);
        const cmp = va < vb ? -1 : va > vb ? 1 : 0;
        return orderDir === "asc" ? cmp : -cmp;
      },
    [columns]
  );
  const comparator = useMemo(
    () => getComparator(orderBy, order),
    [getComparator, orderBy, order]
  );

  const storageKey = useMemo(() => {
    if (!enableRowReorder) return null;
    const colKey = columns.map((c) => String(c.id)).join("|");
    const path = typeof window !== "undefined" ? window.location.pathname : "";
    return `BaseDataTable:rowOrder:${path}:${colKey}`;
  }, [columns, enableRowReorder]);

  const [persistedOrderIds, setPersistedOrderIds] = useState<string[] | null>(
    null
  );

  useEffect(() => {
    if (!storageKey || typeof window === "undefined") return;
    try {
      const raw = window.localStorage.getItem(storageKey);
      if (!raw) {
        setPersistedOrderIds([]);
        return;
      }
      const data = JSON.parse(raw);
      if (Array.isArray(data) && data.every((x) => typeof x === "string")) {
        setPersistedOrderIds(data);
      } else {
        setPersistedOrderIds([]);
      }
    } catch {
      setPersistedOrderIds([]);
    }
  }, [storageKey]);

  const applySavedOrder = Boolean(enableRowReorder) && !orderBy;

  const displayRows = useMemo(() => {
    if (orderBy) return stableSort(rows, comparator);

    if (
      !applySavedOrder ||
      !persistedOrderIds ||
      persistedOrderIds.length === 0
    )
      return rows;

    const indexMap = new Map<string, number>(
      persistedOrderIds.map((id, i) => [id, i])
    );
    const MAX = Number.MAX_SAFE_INTEGER;
    return stableSort(
      rows,
      (a, b) => (indexMap.get(a.id) ?? MAX) - (indexMap.get(b.id) ?? MAX)
    );
  }, [rows, orderBy, comparator, applySavedOrder, persistedOrderIds]);

  const displayRowsRef = useRef(displayRows);
  useEffect(() => {
    displayRowsRef.current = displayRows;
  }, [displayRows]);

  const [menuRow, setMenuRow] = useState<Row | null>(null);
  const [menuPos, setMenuPos] = useState<MenuPosition | null>(null);
  const openMenu = (row: Row) => (e: { currentTarget: HTMLElement }) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setMenuRow(row);
    setMenuPos({ top: Math.round(rect.bottom), left: Math.round(rect.right) });
  };
  const closeMenu = () => {
    setMenuRow(null);
    setMenuPos(null);
  };

  const onSortToggle = (colId: string, sortable?: boolean) => {
    if (!sortable || !onSortChange) return;
    if (orderBy !== colId) onSortChange(colId, "asc");
    else onSortChange(colId, order === "asc" ? "desc" : "asc");
  };

  const isReorderActive =
    Boolean(enableRowReorder) && !orderBy && Boolean(onRowOrderChange);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 5 },
    })
  );

  const [activeRow, setActiveRow] = useState<Row | null>(null);

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      if (!(Boolean(enableRowReorder) && !orderBy && Boolean(onRowOrderChange)))
        return;
      const id = String(event.active.id);

      const row = displayRowsRef.current.find((r) => r.id === id) ?? null;
      setActiveRow(row);
    },
    [enableRowReorder, orderBy, onRowOrderChange]
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      if (
        !(Boolean(enableRowReorder) && !orderBy && Boolean(onRowOrderChange))
      ) {
        setActiveRow(null);
        return;
      }
      const { active, over } = event;

      setActiveRow(null);
      if (!over || active.id === over.id) return;

      const curr = displayRowsRef.current;
      const oldIndex = curr.findIndex((r) => r.id === active.id);
      const newIndex = curr.findIndex((r) => r.id === over.id);
      if (oldIndex === -1 || newIndex === -1) return;

      const next = arrayMove(curr, oldIndex, newIndex);

      if (storageKey && typeof window !== "undefined") {
        const ids = next.map((r) => r.id);
        try {
          window.localStorage.setItem(storageKey, JSON.stringify(ids));
        } catch {
          // ignore storage quota/availability errors
        }
        setPersistedOrderIds(ids);
      }

      onRowOrderChange?.(next);
    },
    [enableRowReorder, orderBy, onRowOrderChange, storageKey]
  );

  const handleDragCancel = useCallback(() => {
    setActiveRow(null);
  }, []);

  const Head = () => (
    <TableHead>
      <TableRow>
        <SelectCell padding="checkbox">
          <Checkbox
            indeterminate={someSelected}
            checked={allSelected}
            onChange={handleToggleAll}
            slotProps={{ input: { "aria-label": "select all" } }}
          />
        </SelectCell>

        {columns.map((col) => {
          const colId = String(col.id);
          const active = orderBy === colId;
          const CellComponent =
            col.sticky === "left"
              ? StickyCellLeft
              : col.sticky === "right"
                ? StickyCellRight
                : TableCell;

          return (
            <CellComponent
              key={colId}
              align={col.align}
              sortDirection={active ? order : false}
              sx={{
                width: col.width,
                whiteSpace: "nowrap",
                ...(col.headerSx || {}),
              }}
            >
              {col.sortable ? (
                <TableSortLabel
                  active={active}
                  direction={active ? order : "asc"}
                  onClick={() => onSortToggle(colId, col.sortable)}
                  IconComponent={active ? undefined : InactiveSortIcon}
                  aria-label={`sort by ${col.label}`}
                  sx={{
                    color: getHeaderColorFromSx(col.headerSx),
                    "&.Mui-active": {
                      color: getHeaderColorFromSx(col.headerSx),
                    },
                  }}
                >
                  {col.label}
                </TableSortLabel>
              ) : (
                col.label
              )}
            </CellComponent>
          );
        })}

        <ActionsCell padding="checkbox" />
      </TableRow>
    </TableHead>
  );

  const SortableRow = ({
    row,
    isSelected,
    highlight,
  }: {
    row: Row;
    isSelected: boolean;
    highlight?: "issue" | undefined;
  }) => {
    const {
      setNodeRef,
      setActivatorNodeRef,
      attributes,
      listeners,
      transform,
      transition,
    } = useSortable({
      id: row.id,
      disabled: !isReorderActive,
    });

    const style: CSSProperties | undefined = isReorderActive
      ? { transform: CSS.Transform.toString(transform), transition }
      : undefined;

    const activatorRef: RefCallback<HTMLButtonElement> = (el) => {
      if (isReorderActive) setActivatorNodeRef(el);
    };

    return (
      <TableRow
        ref={setNodeRef}
        key={row.id}
        hover
        selected={false}
        sx={(theme) => ({
          bgcolor:
            highlight === "issue"
              ? theme.palette.customLightOrange.main + "14"
              : undefined,
        })}
        style={style}
      >
        <SelectCell padding="checkbox" sx={{ zIndex: 2 }}>
          <Checkbox
            checked={isSelected}
            onChange={handleToggleOne(row.id)}
            slotProps={{ input: { "aria-label": `select ${row.id}` } }}
          />
        </SelectCell>

        {columns.map((col) => {
          const CellComponent =
            col.sticky === "left"
              ? StickyCellLeft
              : col.sticky === "right"
                ? StickyCellRight
                : TableCell;
          const content =
            col.renderCell?.(row) ??
            col.getValue?.(row) ??
            toCellContent((row as Record<string, unknown>)[String(col.id)]);

          return (
            <CellComponent
              key={String(col.id)}
              align={col.align}
              sx={{ width: col.width, whiteSpace: "nowrap" }}
            >
              {content}
            </CellComponent>
          );
        })}

        <ActionsCell padding="checkbox" sx={{ zIndex: 2 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 0.5,
              justifyContent: "center",
              width: "100%",
            }}
          >
            <IconButton
              size="small"
              aria-label="row actions"
              onClick={openMenu(row)}
            >
              <MoreHorizIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              aria-label="drag handle"
              sx={{
                borderLeft: 1,
                borderColor: "divider",
                cursor: isReorderActive ? "grab" : "default",
              }}
              ref={isReorderActive ? activatorRef : undefined}
              {...(isReorderActive ? attributes : {})}
              {...(isReorderActive && listeners ? listeners : {})}
              disabled={!isReorderActive}
            >
              <DragIndicatorIcon fontSize="small" />
            </IconButton>
          </Box>
        </ActionsCell>
      </TableRow>
    );
  };

  const StaticRow = ({
    row,
    isSelected,
    highlight,
  }: {
    row: Row;
    isSelected: boolean;
    highlight?: "issue" | undefined;
  }) => (
    <TableRow
      key={row.id}
      hover
      selected={false}
      sx={(theme) => ({
        bgcolor:
          highlight === "issue"
            ? theme.palette.customLightOrange.main + "14"
            : undefined,
      })}
    >
      <SelectCell padding="checkbox" sx={{ zIndex: 2 }}>
        <Checkbox
          checked={isSelected}
          onChange={handleToggleOne(row.id)}
          slotProps={{ input: { "aria-label": `select ${row.id}` } }}
        />
      </SelectCell>

      {columns.map((col) => {
        const CellComponent =
          col.sticky === "left"
            ? StickyCellLeft
            : col.sticky === "right"
              ? StickyCellRight
              : TableCell;
        const content =
          col.renderCell?.(row) ??
          col.getValue?.(row) ??
          toCellContent((row as Record<string, unknown>)[String(col.id)]);

        return (
          <CellComponent
            key={String(col.id)}
            align={col.align}
            sx={{ width: col.width, whiteSpace: "nowrap" }}
          >
            {content}
          </CellComponent>
        );
      })}

      <ActionsCell padding="checkbox" sx={{ zIndex: 2 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 0.5,
            justifyContent: "center",
            width: "100%",
          }}
        >
          <IconButton
            size="small"
            aria-label="row actions"
            onClick={openMenu(row)}
          >
            <MoreHorizIcon fontSize="small" />
          </IconButton>
        </Box>
      </ActionsCell>
    </TableRow>
  );

  const OverlayRow = ({ row }: { row: Row }) => (
    <Paper
      elevation={4}
      sx={{
        px: 1,
        py: 0.5,
        display: "flex",
        alignItems: "center",
        gap: 1,
        borderRadius: 1,
        backgroundColor: "background.paper",
      }}
    >
      {columns.map((col) => {
        const content =
          col.getValue?.(row) ??
          col.renderCell?.(row) ??
          toCellContent((row as Record<string, unknown>)[String(col.id)]);
        return (
          <Box
            key={String(col.id)}
            sx={{
              minWidth: col.width,
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {content}
          </Box>
        );
      })}
    </Paper>
  );

  const table = (
    <TableContainer
      sx={{ maxHeight: typeof height === "number" ? `${height}px` : height }}
    >
      <Table stickyHeader size="small" aria-label="data table">
        <Head />
        <TableBody>
          {displayRows.map((row) => {
            const highlight = getRowHighlight?.(row);
            const isSelected = selectedSet.has(row.id);
            return isReorderActive ? (
              <SortableRow
                key={row.id}
                row={row}
                isSelected={isSelected}
                highlight={highlight}
              />
            ) : (
              <StaticRow
                key={row.id}
                row={row}
                isSelected={isSelected}
                highlight={highlight}
              />
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Paper variant="outlined" sx={{ borderRadius: 2, overflow: "hidden" }}>
      {isReorderActive ? (
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          onDragCancel={handleDragCancel}
          modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
        >
          <SortableContext
            items={displayRows.map((r) => r.id)}
            strategy={verticalListSortingStrategy}
          >
            {table}
          </SortableContext>
          <DragOverlay>
            {activeRow ? <OverlayRow row={activeRow} /> : null}
          </DragOverlay>
        </DndContext>
      ) : (
        table
      )}

      <ActionsMenu
        row={menuRow}
        anchorPosition={menuPos}
        onClose={closeMenu}
        onActionClick={onActionClick}
      />
    </Paper>
  );
}

export default BaseDataTable;
