import { TABLE_WIDTH } from "@/types/table";

import TableCell from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";

export const ActionsCell = styled(TableCell)(({ theme }) => ({
  position: "sticky",
  right: 0,
  zIndex: 3,
  backgroundColor: theme.palette.background.paper,
  width: TABLE_WIDTH.ACTIONS_COL_WIDTH,
  minWidth: TABLE_WIDTH.ACTIONS_COL_WIDTH,
  maxWidth: TABLE_WIDTH.ACTIONS_COL_WIDTH,
  "&::before": {
    content: '""',
    position: "absolute",
    left: 0,
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: theme.palette.divider,
  },
}));
