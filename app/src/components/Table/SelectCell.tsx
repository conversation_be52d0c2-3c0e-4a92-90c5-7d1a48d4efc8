import { TABLE_WIDTH } from "@/types/table";

import TableCell from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";

export const SelectCell = styled(TableCell)(({ theme }) => ({
  position: "sticky",
  left: 0,
  zIndex: 3,
  backgroundColor: theme.palette.background.paper,
  width: TABLE_WIDTH.SELECT_COL_WIDTH,
  minWidth: TABLE_WIDTH.SELECT_COL_WIDTH,
  maxWidth: TABLE_WIDTH.SELECT_COL_WIDTH,
  "&::after": {
    content: '""',
    position: "absolute",
    right: 0,
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: theme.palette.divider,
  },
}));
