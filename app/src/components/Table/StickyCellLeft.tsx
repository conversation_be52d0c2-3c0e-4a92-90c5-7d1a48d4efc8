import { TABLE_WIDTH } from "@/types/table";

import TableCell from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";

export const StickyCellLeft = styled(TableCell)(({ theme }) => ({
  position: "sticky",
  left: TABLE_WIDTH.SELECT_COL_WIDTH,
  zIndex: 2,
  backgroundColor: theme.palette.background.paper,
  "&::after": {
    content: '""',
    position: "absolute",
    right: 0,
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: theme.palette.divider,
  },
}));
