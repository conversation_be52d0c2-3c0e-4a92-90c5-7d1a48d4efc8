import { TABLE_WIDTH } from "@/types/table";

import TableCell from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";

export const StickyCellRight = styled(TableCell)(({ theme }) => ({
  position: "sticky",
  right: TABLE_WIDTH.ACTIONS_COL_WIDTH,
  zIndex: 2,
  backgroundColor: theme.palette.background.paper,
  "&::before": {
    content: '""',
    position: "absolute",
    left: 0,
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: theme.palette.divider,
  },
}));
