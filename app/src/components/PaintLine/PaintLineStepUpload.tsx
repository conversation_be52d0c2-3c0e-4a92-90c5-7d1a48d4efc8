import { PaintLineTable } from "@/components/PaintLine/PaintLineTable";
import { AddMaintenanceBlockButton } from "@/components/Schedule/AddMaintenanceBlockButton";
import { WorkingScheduleSelect } from "@/components/Schedule/WorkingScheduleSelect";
import { ScheduleCsvUploadSection } from "@/components/Upload/PaintLineUploadPanel";
import { ColumnsButton } from "@/components/ui/ColumnsButton";
import { RadioCardOption } from "@/components/ui/RadioCardOption";
import { GRID_WRAPPER_SX } from "@/types/schedule";
import type { Order, PaintLineRow } from "@/types/table";

import Box from "@mui/material/Box";
import RadioGroup from "@mui/material/RadioGroup";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";

export type PaintLineStepUploadProps = {
  scheduleName: string;
  onChangeScheduleName: (name: string) => void;
  onUploadClick: () => void;
  onCsvCardSelect: () => void;

  rows: PaintLineRow[];
  orderBy?: string;
  order: Order;
  onSortChange: (col: string, dir: Order) => void;
  selected: string[];
  onSelectionChange: (ids: string[]) => void;
  onRowOrderChange: (rows: PaintLineRow[]) => void;

  workingNo: string;
  onChangeWorkingNo: (workingNo: string) => void;
};

export const PaintLineStepUpload = ({
  scheduleName,
  onChangeScheduleName,
  onUploadClick,
  onCsvCardSelect,
  rows,
  orderBy,
  order,
  onSortChange,
  selected,
  onSelectionChange,
  onRowOrderChange,
  workingNo,
  onChangeWorkingNo,
}: PaintLineStepUploadProps) => {
  return (
    <>
      <Box sx={GRID_WRAPPER_SX}>
        <Box>
          <Typography
            component="h1"
            sx={{
              fontSize: { xs: 24, md: 32 },
              fontWeight: 800,
              mb: 1.5,
              lineHeight: 1.2,
            }}
          >
            Upload file
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Choose how you wish to upload the three files needed for
            optimisation. (Stock, plan & orders)
          </Typography>

          <RadioGroup value="csv" aria-label="Upload mode selection">
            <RadioCardOption
              value="csv"
              selected
              title="CSV file"
              description="Upload the csv file that contains the schedules you wish to optimise"
              onSelect={onCsvCardSelect}
            />
          </RadioGroup>
        </Box>

        <Box>
          <ScheduleCsvUploadSection
            scheduleName={scheduleName}
            onChangeScheduleName={onChangeScheduleName}
            onUploadClick={onUploadClick}
          />
        </Box>
      </Box>

      {rows.length > 0 && (
        <Box sx={{ mt: 6 }}>
          <Typography sx={{ fontSize: 16, fontWeight: 700, mb: 1 }}>
            {scheduleName ? `Schedule ${scheduleName}` : "Schedule"}
          </Typography>

          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: { xs: "1fr", md: "1fr auto" },
              alignItems: "center",
              rowGap: 1.5,
              mb: 2,
            }}
          >
            <Box>
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ display: "block", mb: 0.5 }}
              >
                Working schedule Number(s)
              </Typography>
              <WorkingScheduleSelect
                value={workingNo}
                onChange={onChangeWorkingNo}
              />
            </Box>

            <Stack
              direction="row"
              spacing={1.5}
              alignItems="center"
              justifyContent={{ xs: "flex-start", md: "flex-end" }}
            >
              <AddMaintenanceBlockButton />
              <ColumnsButton size="small" />
            </Stack>
          </Box>

          <PaintLineTable
            rows={rows}
            orderBy={orderBy}
            order={order}
            onSortChange={onSortChange}
            selected={selected}
            onSelectionChange={onSelectionChange}
            enableRowReorder
            onRowOrderChange={onRowOrderChange}
          />
        </Box>
      )}
    </>
  );
};
