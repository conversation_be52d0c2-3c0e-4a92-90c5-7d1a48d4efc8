import warningSvg from "@/assets/warning.svg";
import { SecondaryNavTabs } from "@/components/CoilNesting/SecondaryNavTabs";
import { PaintLineTable } from "@/components/PaintLine/PaintLineTable";
import { AddMaintenanceBlockButton } from "@/components/Schedule/AddMaintenanceBlockButton";
import { DownloadReportButton } from "@/components/Schedule/DownloadReportButton";
import { ColumnsButton } from "@/components/ui/ColumnsButton";
import { SearchBar } from "@/components/ui/SearchBar";
import type { Order, PaintLineRow } from "@/types/table";

import { memo, useState } from "react";

import BarChartOutlinedIcon from "@mui/icons-material/BarChartOutlined";
import TableRowsOutlinedIcon from "@mui/icons-material/TableRowsOutlined";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import IconButton from "@mui/material/IconButton";
import Paper from "@mui/material/Paper";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useTheme } from "@mui/material/styles";

const ViewToggle = memo(function ViewToggle({
  value,
  onChange,
}: {
  value: "table" | "chart";
  onChange: (next: "table" | "chart") => void;
}) {
  const isTable = value === "table";
  const isChart = value === "chart";

  const baseBtnSx = {
    width: 36,
    height: 32,
    borderRadius: 0,
    color: "text.secondary",
    "&:hover": { bgcolor: "action.hover" },
  } as const;

  const selectedSx = {
    bgcolor: "primary.main",
    color: "primary.contrastText",
    "&:hover": { bgcolor: "primary.dark" },
  } as const;

  return (
    <Paper
      variant="outlined"
      role="group"
      aria-label="Toggle view"
      sx={{
        display: "inline-flex",
        alignItems: "center",
        borderRadius: 1,
        overflow: "hidden",
        bgcolor: "background.paper",
      }}
    >
      <IconButton
        size="small"
        aria-label="Table view"
        aria-pressed={isTable}
        onClick={() => onChange("table")}
        sx={{ ...baseBtnSx, ...(isTable ? selectedSx : null) }}
      >
        <TableRowsOutlinedIcon fontSize="small" />
      </IconButton>
      <Divider orientation="vertical" flexItem sx={{ my: 0.75 }} />
      <IconButton
        size="small"
        aria-label="Chart view"
        aria-pressed={isChart}
        onClick={() => onChange("chart")}
        sx={{ ...baseBtnSx, ...(isChart ? selectedSx : null) }}
      >
        <BarChartOutlinedIcon fontSize="small" />
      </IconButton>
    </Paper>
  );
});

export type PaintLineStepResultsProps = {
  scheduleName: string;
  isOptimising: boolean;
  onStartOptimise: () => void;
  onStopOptimise: () => void;

  rows: PaintLineRow[];
  orderBy?: string;
  order: Order;
  onSortChange: (col: string, dir: Order) => void;
  selected: string[];
  onSelectionChange: (ids: string[]) => void;
  onRowOrderChange: (rows: PaintLineRow[]) => void;

  warningItems: Array<{ title: string; detail?: string }>;
};

export const PaintLineStepResults = ({
  scheduleName,
  isOptimising,
  onStartOptimise,
  onStopOptimise,
  rows,
  orderBy,
  order,
  onSortChange,
  selected,
  onSelectionChange,
  onRowOrderChange,
  warningItems,
}: PaintLineStepResultsProps) => {
  const theme = useTheme();
  const [tab, setTab] = useState(1);
  const [viewMode, setViewMode] = useState<"table" | "chart">("table");
  const [searchQuery, setSearchQuery] = useState("");

  // TODO: use onStopOptimise when exposing Stop controls in this component
  void onStopOptimise;

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography
          component="h1"
          sx={{
            fontSize: { xs: 24, md: 32 },
            fontWeight: 800,
            lineHeight: 1.2,
          }}
        >
          {scheduleName}
        </Typography>
        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            sx={{ textTransform: "none", borderRadius: 1 }}
            onClick={onStartOptimise}
          >
            Optimise
          </Button>
          <DownloadReportButton
            variant="contained"
            sx={{
              textTransform: "none",
              borderRadius: 1,
              bgcolor: "common.black",
              color: "common.white",
              "&:hover": { bgcolor: theme.palette.grey[900] },
            }}
          />
        </Stack>
      </Box>

      {isOptimising ? (
        // Lazy import rather than depend here; leave parent to include OptimizationProgressPanel
        <></>
      ) : (
        <>
          <Box
            sx={{
              mt: 1,
              bgcolor: theme.palette.grey[100],
              borderRadius: 2,
              p: 2,
            }}
          >
            <SecondaryNavTabs
              labels={["Summary", "Warnings", "Configuration", "OD"]}
              value={tab}
              onChange={setTab}
              ariaLabel="Result sections"
              containerBg={theme.palette.grey[100]}
            />
            {tab === 1 && (
              <Stack
                direction={{ xs: "column", md: "row" }}
                spacing={2}
                sx={{ mt: 2 }}
              >
                {warningItems.map((w, idx) => (
                  <Paper
                    key={`${w.title}-${idx}`}
                    variant="outlined"
                    sx={{
                      flex: 1,
                      borderRadius: 2,
                      p: 2,
                      bgcolor: "background.paper",
                    }}
                  >
                    <Stack
                      direction="row"
                      spacing={1.5}
                      alignItems="flex-start"
                    >
                      <Box
                        component="img"
                        src={warningSvg}
                        alt=""
                        aria-hidden={true}
                        sx={{ height: 18, width: "auto" }}
                      />
                      <Box>
                        <Typography
                          sx={{ fontSize: 13, fontWeight: 600, mb: 0.25 }}
                        >
                          {w.title}
                        </Typography>
                        {w.detail ? (
                          <Typography variant="caption" color="text.secondary">
                            {w.detail}
                          </Typography>
                        ) : null}
                      </Box>
                    </Stack>
                  </Paper>
                ))}
              </Stack>
            )}
          </Box>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
              mt: 3,
            }}
          >
            <ViewToggle value={viewMode} onChange={setViewMode} />
          </Box>

          <Stack
            direction={{ xs: "column", md: "row" }}
            spacing={1.5}
            alignItems={{ xs: "stretch", md: "center" }}
            sx={{ mt: 1.5, mb: 2.5 }}
          >
            <Box sx={{ flex: 1, minWidth: 0, maxWidth: { md: 360 } }}>
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                ariaLabel="Search"
                placeholder="Search"
              />
            </Box>

            <Stack
              direction="row"
              spacing={1.5}
              alignItems="center"
              justifyContent={{ xs: "flex-start", md: "flex-end" }}
              sx={{ flex: 1 }}
            >
              <AddMaintenanceBlockButton />
              <ColumnsButton size="small" />
            </Stack>
          </Stack>

          <PaintLineTable
            rows={rows}
            orderBy={orderBy}
            order={order}
            onSortChange={onSortChange}
            selected={selected}
            onSelectionChange={onSelectionChange}
            enableRowReorder
            onRowOrderChange={onRowOrderChange}
          />
        </>
      )}
    </Box>
  );
};
