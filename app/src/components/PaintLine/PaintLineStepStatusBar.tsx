import { CheckOutlineIcon } from "@/components/ui/icons";

import { memo, useEffect, useMemo, useRef } from "react";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import { useTheme } from "@mui/material/styles";

export type PaintLineStepStatusBarProps = {
  scheduleName: string;
  onChangeClick: () => void;
};

export const PaintLineStepStatusBar = memo(function PaintLineStepStatusBar({
  scheduleName,
  onChangeClick,
}: PaintLineStepStatusBarProps) {
  const theme = useTheme();
  const pillWidthSx = useMemo(
    () => ({
      width: { xs: "100%", sm: 720, md: 960, lg: 1120 },
      maxWidth: "100%",
    }),
    []
  );

  const statusRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    statusRef.current?.focus();
  }, []);

  return (
    <Box
      sx={{
        display: "inline-flex",
        flexDirection: "column",
        alignItems: "stretch",
        ml: { xs: 2, sm: 3, md: 5 },
        mb: 3,
      }}
    >
      <Paper
        ref={statusRef}
        tabIndex={-1}
        variant="outlined"
        sx={{
          ...pillWidthSx,
          display: "flex",
          alignItems: "center",
          gap: 2,
          px: 2,
          py: 1,
          borderRadius: 999,
          bgcolor: theme.palette.grey[100],
          borderColor: "divider",
          outline: "none",
        }}
        aria-live="polite"
        role="status"
      >
        <CheckOutlineIcon sx={{ color: "success.main" }} />
        <Typography sx={{ fontSize: 14 }}>
          <Box component="span" sx={{ fontWeight: 600, color: "primary.main" }}>
            {scheduleName}
          </Box>{" "}
          <Box component="span" sx={{ color: "text.secondary" }}>
            has been uploaded
          </Box>
        </Typography>
        <Box sx={{ flex: 1 }} />
        <Button
          size="small"
          variant="text"
          sx={{ textTransform: "none" }}
          onClick={onChangeClick}
        >
          Change
        </Button>
      </Paper>
      <Box
        sx={{
          ...pillWidthSx,
          height: 2,
          bgcolor: theme.palette.grey[300],
          mt: 1.5,
          borderRadius: 1,
        }}
        role="presentation"
        aria-hidden
      />
    </Box>
  );
});
