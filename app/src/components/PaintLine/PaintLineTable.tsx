import warningPng from "@/assets/warning.png";
import { BaseDataTable } from "@/components/Table/BaseDataTable";
import type { Column, PaintLineRow, PaintLineTableProps } from "@/types/table";

import Chip from "@mui/material/Chip";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";

export const PaintLineTable = ({
  rows,
  orderBy,
  order,
  onSortChange,
  selected,
  onSelectionChange,
  enableRowReorder,
  onRowOrderChange,
}: PaintLineTableProps) => {
  const columns: Column<PaintLineRow>[] = [
    { id: "feedCount", label: "Feed count", width: 120 },
    { id: "feedStyle", label: "Feed style", width: 140 },
    { id: "order", label: "Order", width: 140, sortable: true },
    { id: "feedBatch", label: "Feed batch", width: 140, sortable: true },
    { id: "uniqueWidth", label: "Unique Width", width: 150, sortable: true },
    { id: "slitWidths", label: "Slit widths", width: 160 },
    { id: "slitPattern", label: "Slit pattern", width: 160 },
    {
      id: "wastagePct",
      label: "Wastage",
      width: 140,
      renderCell: (r) => (
        <Stack direction="row" spacing={1} alignItems="center">
          <Typography variant="body2" component="span">
            {r.wastagePct.toFixed(1)}%
          </Typography>
          {r.hasIssue && (
            <Chip
              size="small"
              icon={
                <img
                  src={warningPng}
                  alt=""
                  aria-hidden
                  style={{
                    width: 14,
                    height: 14,
                    mixBlendMode: "normal",
                    filter: "none",
                    display: "block",
                  }}
                />
              }
              label=""
              sx={{
                height: 20,
                bgcolor: "transparent",
                color: "inherit",
                px: 0.5,
                "& .MuiChip-icon": { mr: 0 },
              }}
              aria-label="issue"
            />
          )}
        </Stack>
      ),
    },
  ];

  return (
    <BaseDataTable
      columns={columns}
      rows={rows}
      orderBy={orderBy}
      order={order}
      onSortChange={onSortChange}
      selected={selected}
      onSelectionChange={onSelectionChange}
      getRowHighlight={(r) => (r.hasIssue ? "issue" : undefined)}
      height={520}
      enableRowReorder={enableRowReorder}
      onRowOrderChange={onRowOrderChange}
    />
  );
};
