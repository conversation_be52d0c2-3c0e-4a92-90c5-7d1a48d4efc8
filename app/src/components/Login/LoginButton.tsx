import { useAuth } from "@/auth/useAuth";
import { type LoginButtonProps } from "@/types/auth";

import { useCallback } from "react";

import Alert from "@mui/material/Alert";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";

export const LoginButton = ({ className }: LoginButtonProps) => {
  const { login, logout, isAuthenticated, user, loading, error } = useAuth();
  const isMinimal = (className || "").includes("minimal");
  const buttonVariant = isMinimal ? "outlined" : "contained";

  const handleAuth = useCallback(async () => {
    try {
      if (isAuthenticated) {
        await logout();
      } else {
        await login();
      }
    } catch {
      // useAuth is responsible for surfacing errors via `error`
    }
  }, [isAuthenticated, login, logout]);

  return (
    <Stack spacing={2} alignItems="center">
      {error && (
        <Alert severity="error" variant="outlined" sx={{ width: "100%" }}>
          {String(error)}
        </Alert>
      )}

      {loading ? (
        <Button
          variant={buttonVariant}
          disabled
          startIcon={<CircularProgress size={16} color="inherit" />}
          className={className}
        >
          Loading...
        </Button>
      ) : isAuthenticated && user ? (
        <Stack direction="row" spacing={2} alignItems="center">
          <Box>
            <Typography variant="subtitle1">
              {user.name || user.username || "User"}
            </Typography>
            {user.username && (
              <Typography variant="body2" color="text.secondary">
                {user.username}
              </Typography>
            )}
          </Box>
          <Button
            variant={buttonVariant}
            onClick={handleAuth}
            className={className}
            aria-label="Sign out"
          >
            Sign Out
          </Button>
        </Stack>
      ) : (
        <Button
          variant={buttonVariant}
          onClick={handleAuth}
          className={className}
          aria-label="Sign in"
        >
          {isMinimal ? "Sign in" : "Sign in with Microsoft"}
        </Button>
      )}
    </Stack>
  );
};
