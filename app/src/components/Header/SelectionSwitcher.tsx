import { DoubleChevronIcon } from "@/components/ui/DoubleChevronIcon";
import { SectionDot } from "@/components/ui/SectionDot";
import type { SelectionSwitcherProps } from "@/types/header";

import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import type { SelectChangeEvent } from "@mui/material/Select";

export const SelectionSwitcher = ({
  options,
  value,
  onChange,
  size = "small",
  minWidth = 130,
  "aria-label": ariaLabel = "Select section",
}: SelectionSwitcherProps) => {
  const handleChange = (e: SelectChangeEvent<string>) => {
    onChange(e.target.value);
  };

  const current = options.find((o) => o.value === value);
  const isSmall = size === "small";

  return (
    <FormControl size={size} variant="outlined">
      <Select
        value={value}
        onChange={handleChange}
        aria-label={ariaLabel}
        displayEmpty={false}
        IconComponent={DoubleChevronIcon}
        renderValue={() => (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <SectionDot value={current?.value} />
            <Box
              sx={(theme) => ({
                fontSize: 16,
                fontWeight: 600,
                color: theme.palette.text.secondary,
                letterSpacing: "-0.01em",
              })}
            >
              {current?.label ?? value}
            </Box>
          </Box>
        )}
        sx={(theme) => ({
          minWidth,
          height: isSmall ? 32 : 40,
          bgcolor: theme.palette.background.paper,
          borderRadius: "6px",
          "& .MuiSelect-select": {
            py: isSmall ? 0.75 : 1,
            pl: 1.25,
            pr: 4,
            display: "flex",
            alignItems: "center",
          },
          "& .MuiSelect-icon": {
            color: theme.palette.grey[500],
            right: 10,
            top: "50%",
            transform: "translateY(-50%)",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.divider,
            borderWidth: "1px",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[300],
            borderWidth: "1px",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[400],
            borderWidth: "1px",
          },
        })}
        MenuProps={{
          anchorOrigin: { vertical: "bottom", horizontal: "left" },
          transformOrigin: { vertical: "top", horizontal: "left" },
          PaperProps: {
            sx: {
              mt: 0.5,
              borderRadius: 1,
              "& .MuiMenuItem-root": {
                px: 1.25,
                py: 0.75,
                fontSize: 13,
                "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                "&.Mui-selected": {
                  bgcolor: "transparent",
                  "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                },
              },
            },
          },
        }}
      >
        {options.map((opt) => {
          const isSelected = opt.value === value;
          return (
            <MenuItem key={opt.value} value={opt.value}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <SectionDot value={opt.value} />
                <Box
                  component="span"
                  sx={(theme) => ({
                    fontSize: 13,
                    fontWeight: isSelected ? 700 : 500,
                    color: isSelected
                      ? theme.palette.grey[900]
                      : theme.palette.grey[700],
                    letterSpacing: "-0.01em",
                  })}
                >
                  {opt.label}
                </Box>
              </Box>
            </MenuItem>
          );
        })}
      </Select>
    </FormControl>
  );
};

export default SelectionSwitcher;
