import { useAuth } from "@/auth/useAuth";
import { PrimaryNavTabs } from "@/components/Header/PrimaryNavTabs";
import { SelectionSwitcher } from "@/components/Header/SelectionSwitcher";
import { ARRAY_ROUTES } from "@/types/header";

import { useLocation, useNavigate } from "react-router-dom";

import AppBar from "@mui/material/AppBar";
import Avatar from "@mui/material/Avatar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";

export const AppHeader = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, displayName } = useAuth();

  const basePath =
    ARRAY_ROUTES.find((r) => location.pathname.startsWith(r.value))?.value ??
    ARRAY_ROUTES[0].value;

  const fallbackName = user?.name || user?.username || "";
  const fullName =
    (displayName || fallbackName || "").trim() || "Jane Gooseberry";

  const initials = fullName
    .split(/\s+/)
    .filter(Boolean)
    .slice(0, 2)
    .map((n) => n.charAt(0))
    .join("")
    .toUpperCase();

  const handleSectionChange = (next: string) => {
    if (next !== basePath) {
      const preserve = location.pathname.includes("/optimise")
        ? "/optimise"
        : "";
      navigate(`${next}${preserve}`);
    }
  };

  return (
    <AppBar
      position="static"
      elevation={0}
      sx={(theme) => ({
        bgcolor: theme.palette.background.paper,
        borderBottom: `1px solid ${theme.palette.divider}`,
      })}
    >
      <Toolbar
        sx={{
          px: 3,
          minHeight: 52,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          gap: 3,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 4 }}>
          <SelectionSwitcher
            options={ARRAY_ROUTES}
            value={basePath}
            onChange={handleSectionChange}
          />
          <PrimaryNavTabs basePath={basePath} />
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1.5 }}>
          <Typography
            variant="body2"
            noWrap
            sx={(theme) => ({
              fontSize: 13,
              fontWeight: 400,
              color: theme.palette.text.secondary,
              letterSpacing: "-0.01em",
            })}
          >
            {fullName}
          </Typography>
          <Avatar
            sx={(theme) => ({
              width: 28,
              height: 28,
              bgcolor: theme.palette.grey[400],
              fontSize: 11,
              fontWeight: 600,
            })}
            aria-label={fullName ? `${fullName} avatar` : "User avatar"}
            title={fullName}
          >
            {initials || "JG"}
          </Avatar>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader;
