import { ChevronIcon } from "@/components/ui/ChevronIcon";
import { HotelIcon } from "@/components/ui/HotelIcon";
import { type FacilitySelectProps } from "@/types/facility";

import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { type SelectChangeEvent } from "@mui/material/Select";

export const FacilitySelect = ({
  value,
  options,
  onChange,
  ariaLabel = "Select facility",
  minWidth = 160,
}: FacilitySelectProps) => {
  const handleChange = (e: SelectChangeEvent<string>) =>
    onChange(e.target.value);

  const current = options.find((o) => o.id === value);

  return (
    <FormControl size="small" variant="outlined">
      <Select
        value={value}
        onChange={handleChange}
        IconComponent={ChevronIcon}
        aria-label={ariaLabel}
        renderValue={() => (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <HotelIcon />
            <Box
              sx={(theme) => ({
                fontSize: 13,
                fontWeight: 600,
                color: theme.palette.text.secondary,
                letterSpacing: "-0.01em",
              })}
            >
              {current?.label ?? value}
            </Box>
          </Box>
        )}
        sx={(theme) => ({
          minWidth,
          height: 36,
          bgcolor: theme.palette.background.paper,
          borderRadius: "8px",
          "& .MuiSelect-select": {
            py: 0.75,
            pl: 1.25,
            pr: 3.5,
            display: "flex",
            alignItems: "center",
          },
          "& .MuiSelect-icon": {
            color: theme.palette.grey[500],
            right: 10,
            top: "50%",
            transform: "translateY(-50%)",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.divider,
            borderWidth: "1px",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[300],
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[400],
          },
        })}
        MenuProps={{
          anchorOrigin: { vertical: "bottom", horizontal: "left" },
          transformOrigin: { vertical: "top", horizontal: "left" },
          PaperProps: {
            sx: {
              mt: 0.5,
              borderRadius: 1,
              "& .MuiMenuItem-root": {
                px: 1.25,
                py: 0.75,
                fontSize: 13,
                "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                "&.Mui-selected": {
                  bgcolor: "transparent",
                  "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                },
              },
            },
          },
        }}
      >
        {options.map((opt) => (
          <MenuItem key={opt.id} value={opt.id} sx={{ fontSize: 13 }}>
            {opt.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default FacilitySelect;
