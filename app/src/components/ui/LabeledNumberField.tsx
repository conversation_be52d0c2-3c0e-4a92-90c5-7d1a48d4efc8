import { type NumberFieldProps } from "@/types/schedule";

import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";

export const LabeledNumberField = ({
  label,
  value,
  onChange,
  min,
  max,
  step,
  ariaLabel,
}: NumberFieldProps) => (
  <Box sx={{ maxWidth: 236 }}>
    <Typography
      variant="body2"
      noWrap
      title={label}
      sx={{
        fontWeight: 600,
        mb: 0.5,
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      }}
    >
      {label}
    </Typography>
    <TextField
      size="small"
      type="number"
      value={value}
      onChange={(e) => onChange(Number(e.target.value))}
      slotProps={{
        input: {
          inputProps: {
            inputMode: "numeric",
            min,
            max,
            step,
            "aria-label": ariaLabel ?? label,
          },
        },
      }}
      fullWidth
      sx={(theme) => ({
        "& .MuiOutlinedInput-input": { color: theme.palette.grey[500] },
      })}
    />
  </Box>
);
