import nestingLogo from "@/assets/nestinglogo.svg";
import paintlineLogo from "@/assets/paintlinelogo.svg";
import { DOT_SIZE } from "@/types/header";
import { ROUTES } from "@/types/routes";

import Box from "@mui/material/Box";

export const SectionDot = ({ value }: { value?: string }) => {
  const src =
    value === ROUTES.COIL_NESTING
      ? nestingLogo
      : value === ROUTES.PAINT_LINE
        ? paintlineLogo
        : undefined;
  return (
    <Box
      aria-hidden
      sx={{
        width: DOT_SIZE,
        height: DOT_SIZE,
        borderRadius: "50%",
        overflow: "hidden",
        flexShrink: 0,
        boxShadow: "0 0 0 1px rgba(0,0,0,0.06)",
      }}
    >
      {src ? (
        <Box
          component="img"
          src={src}
          alt=""
          aria-hidden
          sx={{
            width: "100%",
            height: "100%",
            display: "block",
            objectFit: "cover",
          }}
        />
      ) : (
        <Box
          sx={(theme) => ({
            width: "100%",
            height: "100%",
            bgcolor: theme.palette.grey[300],
          })}
        />
      )}
    </Box>
  );
};
