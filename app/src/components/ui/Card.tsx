import { type ReactNode } from "react";

import Paper from "@mui/material/Paper";

export const Card = ({
  selected,
  onClick,
  children,
}: {
  selected?: boolean;
  onClick?: () => void;
  children: ReactNode;
}) => (
  <Paper
    variant="outlined"
    onClick={onClick}
    sx={{
      p: 2,
      mb: 1.5,
      borderRadius: 2,
      cursor: "pointer",
      borderColor: selected ? "grey.800" : "divider",
      bgcolor: selected ? "grey.100" : "background.paper",
      transition: "border-color 120ms ease",
      "&:hover": { borderColor: selected ? "grey.800" : "grey.700" },
    }}
  >
    {children}
  </Paper>
);
