import arrowSvg from "@/assets/arrow.svg";
import { type ColumnsButtonProps } from "@/types/table";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";

export const ColumnsButton = ({
  onClick,
  size = "small",
}: ColumnsButtonProps) => (
  <Button
    variant="outlined"
    size={size}
    onClick={onClick}
    sx={{
      borderRadius: 1.5,
      textTransform: "none",
      fontWeight: 700,
      px: 1.5,
      minWidth: 0,
      color: "text.primary",
      border: "1px solid",
      borderColor: "divider",
      "&:hover": { borderColor: "divider", bgcolor: "transparent" },
    }}
  >
    Columns
    <Box
      component="img"
      src={arrowSvg}
      alt=""
      aria-hidden
      sx={{
        ml: 0.5,
        width: 10,
        height: 6,
        display: "inline-block",
        verticalAlign: "middle",
      }}
    />
  </Button>
);
