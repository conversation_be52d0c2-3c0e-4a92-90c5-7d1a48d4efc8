import { SEARCH_INPUT_SX } from "@/types/schedule";
import { type SearchBarProps } from "@/types/table";

import TextField from "@mui/material/TextField";

export const SearchBar = ({
  value,
  onChange,
  placeholder = "Search",
  ariaLabel,
  textFieldProps,
}: SearchBarProps) => (
  <TextField
    value={value}
    onChange={(e) => onChange(e.target.value)}
    size="small"
    placeholder={placeholder}
    aria-label={ariaLabel}
    sx={SEARCH_INPUT_SX}
    slotProps={{ input: { sx: { fontSize: 14 } } }}
    {...textFieldProps}
  />
);
