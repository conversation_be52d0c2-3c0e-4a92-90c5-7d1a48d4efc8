import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";

export type MenuPosition = { top: number; left: number };

export function ActionsMenu<R extends { id: string }>(props: {
  row: R | null;
  anchorPosition: MenuPosition | null;
  onClose: () => void;
  onActionClick?: (row: R, action: "view" | "edit") => void;
}) {
  const { row, anchorPosition, onClose, onActionClick } = props;
  const open = Boolean(anchorPosition);
  return (
    <Menu
      anchorReference="anchorPosition"
      anchorPosition={anchorPosition ?? undefined}
      open={open}
      onClose={onClose}
      transformOrigin={{ vertical: "top", horizontal: "right" }}
      disablePortal={false}
      keepMounted
    >
      <MenuItem
        onClick={() => {
          if (row && onActionClick) onActionClick(row, "view");
          onClose();
        }}
      >
        View details
      </MenuItem>
      <MenuItem
        onClick={() => {
          if (row && onActionClick) onActionClick(row, "edit");
          onClose();
        }}
      >
        Edit
      </MenuItem>
    </Menu>
  );
}
