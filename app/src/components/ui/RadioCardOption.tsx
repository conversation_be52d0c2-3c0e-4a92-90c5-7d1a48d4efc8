import { BlackRadio } from "@/components/ui/BlackRadio";
import { Card } from "@/components/ui/Card";

import Box from "@mui/material/Box";
import FormControlLabel from "@mui/material/FormControlLabel";
import Typography from "@mui/material/Typography";

type RadioCardOptionProps = {
  value: string;
  selected: boolean;
  title: string;
  description: string;
  onSelect: () => void;
};

export const RadioCardOption = ({
  value,
  selected,
  title,
  description,
  onSelect,
}: RadioCardOptionProps) => (
  <Card selected={selected} onClick={onSelect}>
    <FormControlLabel
      value={value}
      control={<BlackRadio size="small" />}
      label={
        <Box>
          <Typography sx={{ fontSize: 14, fontWeight: 600 }}>
            {title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {description}
          </Typography>
        </Box>
      }
    />
  </Card>
);
