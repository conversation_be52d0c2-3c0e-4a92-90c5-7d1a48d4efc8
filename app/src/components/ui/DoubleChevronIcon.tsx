import chevronPng from "@/assets/chevron.svg";
import { type SelectIconProps } from "@/types/header";

import Box from "@mui/material/Box";

export const DoubleChevronIcon = ({ className }: SelectIconProps) => (
  <Box
    className={className}
    sx={{
      display: "flex",
      flexDirection: "column",
      gap: "1px",
      pointerEvents: "none",
    }}
    role="presentation"
  >
    <Box
      component="img"
      src={chevronPng}
      alt=""
      aria-hidden
      sx={{
        width: 18,
        height: 12,
        display: "block",
        transform: "rotate(180deg)",
      }}
    />
  </Box>
);
