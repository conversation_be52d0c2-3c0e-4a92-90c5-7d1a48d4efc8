import { CheckOutlineIcon } from "@/components/ui/icons";

import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";

type StepDotsProps = { activeStep?: number };

export const StepDots = ({ activeStep = 0 }: StepDotsProps) => {
  const steps = [
    "1. Upload files",
    "2. Add priority & volume",
    "3. Configure parameters",
    "4. Run optimisation",
    "5. View result",
  ] as const;

  return (
    <Box
      role="list"
      sx={{
        display: "flex",
        width: "100%",
        alignItems: "center",
        justifyContent: { xs: "flex-start", sm: "space-evenly" },
        flexWrap: { xs: "wrap", sm: "nowrap" },
        rowGap: { xs: 1, sm: 0 },
        color: "text.secondary",
        fontSize: 12,
      }}
    >
      {steps.map((s, i) => (
        <Box
          key={s}
          sx={{
            display: "inline-flex",
            alignItems: "center",
            flex: { xs: "0 1 auto", sm: "0 0 auto" },
            minWidth: { sm: 0 },
          }}
        >
          <Box
            component="span"
            aria-hidden
            sx={{
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              lineHeight: 1,
              width: 14,
              height: 14,
              mr: 1.5,
            }}
          >
            {i < activeStep ? (
              <CheckOutlineIcon sx={{ color: "success.main", fontSize: 14 }} />
            ) : i === activeStep ? (
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: "50%",
                  bgcolor: "info.main",
                }}
              />
            ) : (
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: "50%",
                  bgcolor: "grey.400",
                }}
              />
            )}
          </Box>
          <Typography
            component="span"
            role="listitem"
            sx={{
              fontWeight: i === activeStep ? 700 : 500,
              color: i === activeStep ? "text.primary" : "text.secondary",
              whiteSpace: { xs: "normal", sm: "nowrap" },
            }}
          >
            {s}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};
