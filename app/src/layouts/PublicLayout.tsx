import { useAuth } from "@/auth/useAuth";
import { ROUTES } from "@/types/routes";

import { Navigate, Outlet, useLocation } from "react-router-dom";

import Box from "@mui/material/Box";
import CircularProgress from "@mui/material/CircularProgress";

export const PublicLayout = () => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <Box sx={{ minHeight: "100vh", display: "grid", placeItems: "center" }}>
        <CircularProgress aria-label="Loading" />
      </Box>
    );
  }

  if (isAuthenticated && location.pathname === ROUTES.LOGIN_PATH) {
    return <Navigate to={ROUTES.COIL_NESTING} replace />;
  }

  return <Outlet />;
};
