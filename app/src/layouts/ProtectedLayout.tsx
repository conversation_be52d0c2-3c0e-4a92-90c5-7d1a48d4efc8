import { useAuth } from "@/auth/useAuth";
import { AppHeader } from "@/components/Header/AppHeader";
import { ROUTES } from "@/types/routes";

import { Suspense } from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";

import Box from "@mui/material/Box";
import CircularProgress from "@mui/material/CircularProgress";

export const ProtectedLayout = () => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <Box sx={{ minHeight: "100vh", display: "grid", placeItems: "center" }}>
        <CircularProgress aria-label="Loading" />
      </Box>
    );
  }

  if (!isAuthenticated) {
    return (
      <Navigate to={ROUTES.LOGIN_PATH} replace state={{ from: location }} />
    );
  }

  return (
    <Box sx={{ minHeight: "100vh", display: "flex", flexDirection: "column" }}>
      <AppHeader />
      <Box component="main" sx={{ flex: 1, bgcolor: "background.paper" }}>
        <Suspense
          fallback={
            <Box sx={{ display: "grid", placeItems: "center", p: 2 }}>
              <CircularProgress aria-label="Loading content" />
            </Box>
          }
        >
          <Outlet />
        </Suspense>
      </Box>
    </Box>
  );
};
