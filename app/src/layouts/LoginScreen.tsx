import { useAuth } from "@/auth/useAuth";
import { LoginButton } from "@/components/Login/LoginButton";

import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

import Box from "@mui/material/Box";

export const LoginScreen = () => {
  const { isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();

  const shouldRedirect = !loading && isAuthenticated;

  useEffect(() => {
    if (shouldRedirect) {
      navigate("/coil-nesting", { replace: true });
    }
  }, [shouldRedirect, navigate]);

  if (shouldRedirect) {
    return null;
  }

  return (
    <Box
      component="main"
      aria-busy={loading}
      sx={{
        minHeight: "100vh",
        display: "grid",
        placeItems: "center",
        bgcolor: "background.default",
      }}
    >
      <LoginButton className="minimal" />
    </Box>
  );
};
