import type { ThemeOptions } from "@mui/material/styles";

export const typography: ThemeOptions["typography"] = {
  fontFamily: '"Maison Neue", Arial, sans-serif',
  h1: {
    fontWeight: 800,
    fontSize: "3.5rem",
    lineHeight: 1.15,
    letterSpacing: "-0.015em",
  },
  h2: {
    fontWeight: 800,
    fontSize: "2.5rem",
    lineHeight: 1.2,
    letterSpacing: "-0.005em",
  },
  h3: {
    fontWeight: 700,
    fontSize: "2rem",
    lineHeight: 1.25,
    letterSpacing: "0em",
  },
  h4: {
    fontWeight: 700,
    fontSize: "1.5rem",
    lineHeight: 1.3,
    letterSpacing: "0.005em",
  },
  h5: {
    fontWeight: 700,
    fontSize: "1.25rem",
    lineHeight: 1.35,
    letterSpacing: "0.0075em",
  },
  h6: {
    fontWeight: 500,
    fontSize: "1.1rem",
    lineHeight: 1.4,
    letterSpacing: "0.01em",
  },
  subtitle1: {
    fontWeight: 500,
    fontSize: "1rem",
    lineHeight: 1.5,
    letterSpacing: "0.01em",
  },
  body1: {
    fontWeight: 500,
    fontSize: "1rem",
    lineHeight: 1.5,
    letterSpacing: "0.01em",
  },
  body2: {
    fontWeight: 500,
    fontSize: "0.875rem",
    lineHeight: 1.45,
    letterSpacing: "0.01em",
  },
  button: {
    fontWeight: 700,
    fontSize: "0.9375rem",
    lineHeight: 1.75,
    letterSpacing: "0.02em",
    textTransform: "none",
  },
  caption: {
    fontWeight: 500,
    fontSize: "0.75rem",
    lineHeight: 1.6,
    letterSpacing: "0.025em",
  },
};
