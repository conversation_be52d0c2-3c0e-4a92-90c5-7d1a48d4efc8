import { SecondaryNavTabs } from "@/components/CoilNesting/SecondaryNavTabs";
import { CreateScheduleSplitButton } from "@/components/Schedule/CreateScheduleSplitButton";
import { DownloadReportButton } from "@/components/Schedule/DownloadReportButton";
import { ViewFullScheduleButton } from "@/components/Schedule/ViewFullScheduleButton";
import { ROUTES } from "@/types/routes";

import { useState } from "react";
import { useNavigate } from "react-router-dom";

import Box from "@mui/material/Box";
import Container from "@mui/material/Container";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

export const PaintLine = () => {
  const navigate = useNavigate();

  const [tabIndex, setTabIndex] = useState(0);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography
          variant="h4"
          sx={{
            fontSize: 28,
            fontWeight: 700,
            color: "grey.900",
            letterSpacing: "-0.02em",
          }}
        >
          Welcome back!
        </Typography>
        <CreateScheduleSplitButton
          onCreate={() => navigate(ROUTES.PAINT_LINE_OPTIMISE)}
        />
      </Box>

      <SecondaryNavTabs
        labels={["Home", "Past schedules", "Configuration"]}
        value={tabIndex}
        onChange={setTabIndex}
      />

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mt: 3,
          mb: 1.5,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: 20,
            fontWeight: 700,
            color: "grey.900",
            letterSpacing: "-0.01em",
          }}
        >
          Summary of most recent optimisation
        </Typography>
        <ViewFullScheduleButton />
      </Box>

      <Paper
        variant="outlined"
        sx={{
          bgcolor: "grey.100",
          borderColor: "divider",
          borderRadius: 2,
          height: 180,
        }}
      />

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
        <DownloadReportButton />
      </Box>
    </Container>
  );
};
