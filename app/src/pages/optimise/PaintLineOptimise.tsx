import { PaintLineStepResults } from "@/components/PaintLine/PaintLineStepResults";
import { PaintLineStepStatusBar } from "@/components/PaintLine/PaintLineStepStatusBar";
import { PaintLineStepUpload } from "@/components/PaintLine/PaintLineStepUpload";
import { OptimizationProgressPanel } from "@/components/Schedule/OptimizationProgressPanel";
import { usePaintLineOptimise } from "@/pages/optimise/usePaintLineOptimise";
import type { PaintLineRow } from "@/types/table";

import { useCallback, useEffect, useMemo, useState } from "react";

import Container from "@mui/material/Container";

const noopRowOrderChange: (nextRows: PaintLineRow[]) => void = () => {};

export function PaintLineOptimise(): JSX.Element {
  const {
    state: { scheduleName, isUploaded, workingNo, orderBy, order, selectedIds },
    actions: {
      setScheduleName,
      upload,
      resetUpload,
      setWorkingNo,
      onSortChange,
      setSelectedIds,
      onCsvCardSelect,
    },
    rows,
  } = usePaintLineOptimise();

  const [isOptimising, setIsOptimising] = useState(false);

  useEffect(() => {
    if (!isUploaded && isOptimising) {
      setIsOptimising(false);
    }
  }, [isUploaded, isOptimising]);

  const startOptimise = useCallback(() => setIsOptimising(true), []);
  const stopOptimise = useCallback(() => setIsOptimising(false), []);

  const warningItems = useMemo(
    () => [
      { title: "Not enough paint available", detail: "Sched No 3, 10 ,11" },
      { title: "Another warning" },
      { title: "Another warning" },
    ],
    []
  );

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {!isUploaded ? (
        <PaintLineStepUpload
          scheduleName={scheduleName}
          onChangeScheduleName={setScheduleName}
          onUploadClick={upload}
          onCsvCardSelect={onCsvCardSelect}
          rows={rows}
          orderBy={orderBy}
          order={order}
          onSortChange={onSortChange}
          selected={selectedIds}
          onSelectionChange={setSelectedIds}
          onRowOrderChange={noopRowOrderChange}
          workingNo={workingNo}
          onChangeWorkingNo={setWorkingNo}
        />
      ) : (
        <>
          <PaintLineStepStatusBar
            scheduleName={scheduleName}
            onChangeClick={resetUpload}
          />

          <PaintLineStepResults
            scheduleName={scheduleName}
            isOptimising={isOptimising}
            onStartOptimise={startOptimise}
            onStopOptimise={stopOptimise}
            rows={rows}
            orderBy={orderBy}
            order={order}
            onSortChange={onSortChange}
            selected={selectedIds}
            onSelectionChange={setSelectedIds}
            onRowOrderChange={noopRowOrderChange}
            warningItems={warningItems}
          />

          {isOptimising ? (
            <OptimizationProgressPanel hideBestSoFar onStop={stopOptimise} />
          ) : null}
        </>
      )}
    </Container>
  );
}
