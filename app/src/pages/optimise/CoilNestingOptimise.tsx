import { Step1Upload } from "@/components/CoilNesting/Step1Upload";
import { Step2Priority } from "@/components/CoilNesting/Step2Priority";
import { Step3Parameters } from "@/components/CoilNesting/Step3Parameters";
import { Step4Run } from "@/components/CoilNesting/Step4Run";
import { Step5Result } from "@/components/CoilNesting/Step5Result";
import { StepDots } from "@/components/ui/StepDots";
import { useCoilNestingOptimise } from "@/pages/optimise/useCoilNestingOptimise";
import { CONTINUE_BTN_SX, STEP_HEADER_GAP } from "@/types/schedule";

import { useCallback } from "react";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";

enum Steps {
  Upload = 0,
  Priority = 1,
  Parameters = 2,
  Run = 3,
  Result = 4,
}

export function CoilNestingOptimise(): JSX.Element {
  const {
    state: { step, mode, files, priority },
    actions: {
      setMode,
      setFileName,
      setPrioritySearch,
      setSelectedPriorityIds,
      nextStep,
    },
    derived: { hasPlanError, isContinueDisabled },
    priorityRows,
  } = useCoilNestingOptimise();

  const handleZipNameChange = useCallback(
    (v: string) => setFileName("zipName", v),
    [setFileName]
  );
  const handleStockNameChange = useCallback(
    (v: string) => setFileName("stockName", v),
    [setFileName]
  );
  const handlePlanNameChange = useCallback(
    (v: string) => setFileName("planName", v),
    [setFileName]
  );
  const handleOrdersNameChange = useCallback(
    (v: string) => setFileName("ordersName", v),
    [setFileName]
  );

  const isBeforeParameters = step < Steps.Parameters;

  return (
    <Container maxWidth="xl" sx={{ py: 5 }}>
      <Box sx={{ mb: STEP_HEADER_GAP }}>
        <StepDots activeStep={step} />
      </Box>

      {step === Steps.Priority && (
        <Box sx={{ mb: 2 }}>
          <Typography
            component="h1"
            sx={{
              fontSize: { xs: 24, md: 32 },
              fontWeight: 800,
              mb: 1.5,
              lineHeight: 1.2,
            }}
          >
            Add priority
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Fill out priorities <strong>IN KG</strong> for orders as necessary
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            NOTE: Any priorities left blank will default to 0kg. There is no
            minimum requirement of mass to be produced
          </Typography>
        </Box>
      )}

      {step === Steps.Upload && (
        <Step1Upload
          mode={mode}
          onModeChange={setMode}
          zipName={files.zipName}
          stockName={files.stockName}
          planName={files.planName}
          ordersName={files.ordersName}
          onZipNameChange={handleZipNameChange}
          onStockNameChange={handleStockNameChange}
          onPlanNameChange={handlePlanNameChange}
          onOrdersNameChange={handleOrdersNameChange}
          hasPlanError={hasPlanError}
        />
      )}

      {step === Steps.Priority && (
        <Step2Priority
          rows={priorityRows}
          searchValue={priority.search}
          onSearchChange={setPrioritySearch}
          selectedIds={priority.selectedIds}
          onSelectionChange={setSelectedPriorityIds}
        />
      )}

      {step === Steps.Parameters && (
        <Step3Parameters onStartOptimisation={nextStep} />
      )}
      {step === Steps.Run && <Step4Run onUseThisOptimisation={nextStep} />}
      {step === Steps.Result && <Step5Result />}

      {isBeforeParameters && (
        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 1 }}>
          <Button
            variant="contained"
            disabled={isContinueDisabled}
            onClick={nextStep}
            sx={CONTINUE_BTN_SX}
          >
            Continue
          </Button>
        </Box>
      )}
    </Container>
  );
}
