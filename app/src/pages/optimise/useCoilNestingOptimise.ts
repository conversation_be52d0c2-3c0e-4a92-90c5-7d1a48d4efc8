import { type WizardM<PERSON>, type WizardStep } from "@/types/schedule";
import type { PriorityRow } from "@/types/table";

import { useMemo, useReducer } from "react";

type FilesState = {
  zipName: string;
  stockName: string;
  planName: string;
  ordersName: string;
};

type PriorityState = {
  search: string;
  selectedIds: string[];
};

type WizardState = {
  step: WizardStep;
  mode: WizardMode;
  files: FilesState;
  priority: PriorityState;
};

type Action =
  | { type: "SET_MODE"; payload: WizardMode }
  | { type: "SET_FILE_NAME"; payload: { key: keyof FilesState; value: string } }
  | { type: "SET_PRIORITY_SEARCH"; payload: string }
  | { type: "SET_PRIORITY_SELECTED"; payload: string[] }
  | { type: "NEXT_STEP" }
  | { type: "PREV_STEP" };

const MIN_STEP = 0;
const MAX_STEP = 4;

const INITIAL_STATE: WizardState = {
  step: 0,
  mode: "individual",
  files: {
    zipName: "2323_3294856.zip",
    stockName: "2323_3294856.csx",
    planName: "1234_235457_Plan_order",
    ordersName: "2323_3294856.csx",
  },
  priority: {
    search: "",
    selectedIds: [],
  },
};

const clampStep = (n: number): WizardStep => {
  const clamped = Math.max(MIN_STEP, Math.min(Math.trunc(n), MAX_STEP));
  return clamped as WizardStep;
};

function reducer(state: WizardState, action: Action): WizardState {
  switch (action.type) {
    case "SET_MODE": {
      const mode = action.payload;
      return { ...state, mode };
    }
    case "SET_FILE_NAME": {
      const { key, value } = action.payload;
      return { ...state, files: { ...state.files, [key]: value } };
    }
    case "SET_PRIORITY_SEARCH": {
      return {
        ...state,
        priority: { ...state.priority, search: action.payload },
      };
    }
    case "SET_PRIORITY_SELECTED": {
      return {
        ...state,
        priority: { ...state.priority, selectedIds: action.payload },
      };
    }
    case "NEXT_STEP": {
      return { ...state, step: clampStep(state.step + 1) };
    }
    case "PREV_STEP": {
      return { ...state, step: clampStep(state.step - 1) };
    }
    default:
      return state;
  }
}

const PRIORITY_ROWS_DEMO: PriorityRow[] = Array.from({ length: 8 }, (_, i) => ({
  id: String(i + 1),
  order: "••••",
  material: "••••",
  materialDescription: "•••••••• ••••",
  ou: "KG",
  mrpController: "••••",
  comOn: "••••",
  itemQuantity: "••••",
  ou2: "KG",
  basicFinishDay: "••••",
  priority: "",
  vol: "",
}));

export function useCoilNestingOptimise() {
  const [state, dispatch] = useReducer(reducer, INITIAL_STATE);

  const hasPlanError = /order/i.test(state.files.planName);

  const isContinueDisabled = hasPlanError;

  const actions = useMemo(
    () => ({
      setMode: (mode: WizardMode) =>
        dispatch({ type: "SET_MODE", payload: mode }),
      setFileName: (key: keyof FilesState, value: string) =>
        dispatch({ type: "SET_FILE_NAME", payload: { key, value } }),
      setPrioritySearch: (v: string) =>
        dispatch({ type: "SET_PRIORITY_SEARCH", payload: v }),
      setSelectedPriorityIds: (ids: string[]) =>
        dispatch({ type: "SET_PRIORITY_SELECTED", payload: ids }),
      nextStep: () => dispatch({ type: "NEXT_STEP" }),
      prevStep: () => dispatch({ type: "PREV_STEP" }),
    }),
    [dispatch]
  );

  return {
    state,
    actions,
    derived: {
      hasPlanError,
      isContinueDisabled,
    },
    priorityRows: PRIORITY_ROWS_DEMO,
  };
}
