import type { Order, PaintLineRow } from "@/types/table";

import { useCallback, useMemo, useState } from "react";

export type PaintLineState = {
  scheduleName: string;
  isUploaded: boolean;
  workingNo: string;
  orderBy?: string;
  order: Order;
  selectedIds: string[];
};

export type PaintLineActions = {
  setScheduleName: (name: string) => void;
  upload: () => void;
  resetUpload: () => void;
  setWorkingNo: (workingNo: string) => void;
  onSortChange: (col: string, dir: Order) => void;
  setSelectedIds: (ids: string[]) => void;
  onCsvCardSelect: () => void;
};

export type UsePaintLineOptimiseReturn = {
  state: PaintLineState;
  actions: PaintLineActions;
  rows: PaintLineRow[];
};

const createMockRows = (): PaintLineRow[] =>
  Array.from({ length: 10 }).map((_, i) => ({
    id: String(i + 1),
    feedCount: (i % 3) + 1,
    feedStyle: ["A", "B", "C"][i % 3],
    order: `12${345 + i}`,
    feedBatch: `B-${10 + i}`,
    uniqueWidth: 1180 + ((i * 5) % 40),
    slitWidths: "100,150,200",
    slitPattern: ["AAB", "ABC", "CCC"][i % 3],
    wastagePct: 1.2 + (i % 5) * 0.8,
    hasIssue: i % 4 === 1,
  }));

export function usePaintLineOptimise(): UsePaintLineOptimiseReturn {
  const [scheduleName, setScheduleName] = useState<string>("");
  const [isUploaded, setIsUploaded] = useState<boolean>(false);
  const [workingNo, setWorkingNo] = useState<string>("");

  const [orderBy, setOrderBy] = useState<string | undefined>(undefined);
  const [order, setOrder] = useState<Order>("asc");
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const rows = useMemo<PaintLineRow[]>(() => createMockRows(), []);

  const onSortChange = useCallback((col: string, dir: Order) => {
    setOrderBy(col);
    setOrder(dir);
  }, []);

  const upload = useCallback(() => {
    setIsUploaded(scheduleName.trim().length > 0);
  }, [scheduleName]);

  const resetUpload = useCallback(() => {
    setIsUploaded(false);
    setScheduleName("");
    setSelectedIds([]);
  }, []);

  const onCsvCardSelect = useCallback(() => {}, []);

  const state: PaintLineState = {
    scheduleName,
    isUploaded,
    workingNo,
    orderBy,
    order,
    selectedIds,
  };

  const actions: PaintLineActions = {
    setScheduleName,
    upload,
    resetUpload,
    setWorkingNo,
    onSortChange,
    setSelectedIds,
    onCsvCardSelect,
  };

  return { state, actions, rows };
}
