import { SecondaryNavTabs } from "@/components/CoilNesting/SecondaryNavTabs";
import { FacilitySelect } from "@/components/Facility/FacilitySelect";
import { CreateScheduleSplitButton } from "@/components/Schedule/CreateScheduleSplitButton";
import { ViewFullScheduleButton } from "@/components/Schedule/ViewFullScheduleButton";
import { ROUTES } from "@/types/routes";

import { useState } from "react";
import { useNavigate } from "react-router-dom";

import Box from "@mui/material/Box";
import Container from "@mui/material/Container";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

export const CoilNesting = () => {
  const navigate = useNavigate();
  const [facility, setFacility] = useState("grandville");

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography
          variant="h4"
          sx={{
            fontSize: 28,
            fontWeight: 700,
            color: "grey.900",
            letterSpacing: "-0.02em",
          }}
        >
          Welcome back!
        </Typography>
        <CreateScheduleSplitButton
          onCreate={() => navigate(ROUTES.COIL_NESTING_OPTIMISE)}
        />
      </Box>

      <Box sx={{ mb: 2 }}>
        <FacilitySelect
          value={facility}
          onChange={setFacility}
          options={[{ id: "grandville", label: "Grandville" }]}
        />
      </Box>

      <SecondaryNavTabs basePath={ROUTES.COIL_NESTING} />

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mt: 3,
          mb: 1.5,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: 20,
            fontWeight: 700,
            color: "grey.900",
            letterSpacing: "-0.01em",
          }}
        >
          Summary of most recent optimisation
        </Typography>
        <ViewFullScheduleButton />
      </Box>

      <Paper
        variant="outlined"
        sx={{
          bgcolor: "grey.100",
          borderColor: "divider",
          borderRadius: 2,
          height: 180,
        }}
      />
    </Container>
  );
};
