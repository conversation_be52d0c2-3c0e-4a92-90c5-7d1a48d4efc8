// React context for Azure AD authentication
import { type ReactNode, useEffect, useMemo, useState } from "react";

import { InteractionRequiredAuthError } from "@azure/msal-browser";
import type { AccountInfo, AuthenticationResult } from "@azure/msal-browser";

import { AuthContext, type AuthContextType } from "./AuthContext";
import { getActiveAccount, loginRequest, msalInstance } from "./authConfig";

interface AuthProviderProps {
  children: ReactNode;
}

const cleanPart = (part: string): string => {
  let p = part.trim();
  // Remove a stray trailing single uppercase initial glued to the end, e.g., "PhamA" -> "Pham"
  p = p.replace(/([a-z])([A-Z])$/, "$1");
  // Collapse spaces
  return p.replace(/\s+/g, " ");
};

const finalize = (name: string): string => {
  let n = name.replace(/\s+/g, " ").trim();
  // As a last guard, if the very end looks like a glued initial, drop it
  n = n.replace(/([a-z])([A-Z])$/, "$1");
  return n;
};

const deriveDisplayName = (acct: AccountInfo | null): string | null => {
  if (!acct) return null;
  const claims = (acct.idTokenClaims ?? {}) as Record<string, unknown>;
  const given = (claims["given_name"] as string | undefined)?.trim();
  const family = (claims["family_name"] as string | undefined)?.trim();

  // Prefer given + family when available (most reliable order and spacing)
  if (given && family) {
    return finalize(`${cleanPart(given)} ${cleanPart(family)}`);
  }

  // Otherwise try claims.name or account fields
  const raw =
    (claims["name"] as string | undefined) ||
    acct.name ||
    acct.username ||
    null;
  if (!raw) return null;

  let name = String(raw).trim();

  // If format is "Last, First[Middle?]", flip to "First[Middle?] Last"
  if (name.includes(",")) {
    const [last, rest] = name.split(",", 2).map((s) => s.trim());
    const firstPart = cleanPart(rest);
    const lastPart = cleanPart(last);
    name = `${firstPart} ${lastPart}`;
  }

  return finalize(name);
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<AccountInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Initialize MSAL
        await msalInstance.initialize();

        // Check if user is already logged in
        const account = getActiveAccount();
        if (account) {
          setUser(account);
          setIsAuthenticated(true);
        }
      } catch (err) {
        console.error("Failed to initialize authentication:", err);
        setError("Failed to initialize authentication");
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const response: AuthenticationResult =
        await msalInstance.loginPopup(loginRequest);

      if (response.account) {
        setUser(response.account);
        setIsAuthenticated(true);
        msalInstance.setActiveAccount(response.account);
      }
    } catch (err) {
      console.error("Login failed:", err);
      setError("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setLoading(true);
      await msalInstance.logoutPopup();
      setUser(null);
      setIsAuthenticated(false);
    } catch (err) {
      console.error("Logout failed:", err);
      setError("Logout failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const acquireToken = async (): Promise<string | null> => {
    try {
      const account = getActiveAccount();
      if (!account) {
        throw new Error("No active account");
      }

      const response = await msalInstance.acquireTokenSilent({
        ...loginRequest,
        account: account,
      });

      return response.accessToken;
    } catch (err) {
      if (err instanceof InteractionRequiredAuthError) {
        // Fallback to interactive method
        try {
          const response = await msalInstance.acquireTokenPopup(loginRequest);
          return response.accessToken;
        } catch (interactiveErr) {
          console.error(
            "Interactive token acquisition failed:",
            interactiveErr
          );
          return null;
        }
      } else {
        console.error("Token acquisition failed:", err);
        return null;
      }
    }
  };

  const displayName = useMemo(() => deriveDisplayName(user), [user]);

  const value: AuthContextType = {
    isAuthenticated,
    user,
    displayName,
    login,
    logout,
    acquireToken,
    loading,
    error,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
