import js from "@eslint/js";
import eslint<PERSON>onfig<PERSON>rettier from "eslint-config-prettier/flat";
import jsxA11y from "eslint-plugin-jsx-a11y";
import eslintPluginPrettier from "eslint-plugin-prettier/recommended";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import globals from "globals";
import tseslint from "typescript-eslint";

export default tseslint.config([
  {
    ignores: ["dist"],
  },
  {
    files: ["**/*.{ts,tsx}"],
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
      react.configs.flat.recommended,
      react.configs.flat["jsx-runtime"],
      reactHooks.configs["recommended-latest"],
      jsxA11y.flatConfigs.recommended,
      reactRefresh.configs.vite,
      eslintConfigPrettier,
      eslintPluginPrettier,
    ],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    settings: {
      react: {
        version: "detect",
      },
    },
    rules: {
      "react/prop-types": "off",
      // Discourage React.FC and React.FunctionComponent usage
      "@typescript-eslint/no-restricted-types": [
        "error",
        {
          types: {
            "React.FC": {
              message:
                "Avoid using React.FC. Use regular function components with explicit props typing instead.",
              suggest: ["function"],
            },
            "React.FunctionComponent": {
              message:
                "Avoid using React.FunctionComponent. Use regular function components with explicit props typing instead.",
              suggest: ["function"],
            },
            FC: {
              message:
                "Avoid using FC. Import and use regular function components with explicit props typing instead.",
              suggest: ["function"],
            },
            FunctionComponent: {
              message:
                "Avoid using FunctionComponent. Use regular function components with explicit props typing instead.",
              suggest: ["function"],
            },
          },
        },
      ],
    },
  },
]);
