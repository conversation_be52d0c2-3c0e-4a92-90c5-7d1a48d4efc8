{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit --project tsconfig.app.json", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@azure/msal-browser": "^3.10.0", "@azure/msal-react": "^2.1.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.1", "@tanstack/react-table": "^8.21.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.8.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "tailwindcss": "^4.1.12", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vite": "^7.0.4"}}